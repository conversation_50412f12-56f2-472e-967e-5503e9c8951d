#!/usr/bin/env node

// <PERSON>ript to create a test sensor in MongoDB
const { MongoClient, ObjectId } = require('mongodb');

const config = {
  mongodb: {
    url: 'mongodb://triomobil-api:<EMAIL>:27017,facility-api-dev-shard-00-01.4faai.mongodb.net:27017,facility-api-dev-shard-00-02.4faai.mongodb.net:27017/api?ssl=true&replicaSet=facility-api-dev-shard-0&authSource=admin&readPreference=secondaryPreferred'
  }
};

async function createTestSensor() {
  console.log('🏗️  Creating test sensor in MongoDB...');
  
  const client = new MongoClient(config.mongodb.url);
  try {
    await client.connect();
    const db = client.db('api');
    
    // Check if sensor already exists
    const existingSensor = await db.collection('sensor').findOne({ addr: 'TEST_SENSOR_001' });
    if (existingSensor) {
      console.log(`✅ Test sensor already exists with ID: ${existingSensor._id}`);
      console.log(`   Address: ${existingSensor.addr}`);
      console.log(`   Label: ${existingSensor.label || 'N/A'}`);
      console.log(`   Inputs: ${JSON.stringify(existingSensor.inputs || {})}`);
      return existingSensor;
    }
    
    const testSensor = {
      addr: 'TEST_SENSOR_001',
      label: 'Test Sensor for Cache Testing',
      inputs: {
        analog1: { 
          unit: 'V', 
          min: 0, 
          max: 10,
          formula: 'x' // Simple pass-through formula
        },
        digital1: { 
          type: 'boolean',
          description: 'Digital input 1'
        }
      },
      customerId: new ObjectId('507f1f77bcf86cd799439011'), // Example ObjectId
      dealerId: new ObjectId('507f1f77bcf86cd799439012'),
      audit: {
        createdAt: new Date(),
        createdBy: 'test-script'
      }
    };
    
    const result = await db.collection('sensor').insertOne(testSensor);
    console.log(`✅ Created test sensor with ID: ${result.insertedId}`);
    console.log(`   Address: ${testSensor.addr}`);
    console.log(`   Label: ${testSensor.label}`);
    console.log(`   Inputs: ${JSON.stringify(testSensor.inputs)}`);
    
    return { ...testSensor, _id: result.insertedId };
  } catch (error) {
    console.error('❌ Error creating test sensor:', error.message);
    return null;
  } finally {
    await client.close();
  }
}

async function findExistingSensors() {
  console.log('🔍 Finding existing sensors...');
  
  const client = new MongoClient(config.mongodb.url);
  try {
    await client.connect();
    const db = client.db('api');
    const sensors = await db.collection('sensor')
      .find({ 'audit.deletedAt': { $exists: false } })
      .limit(10)
      .toArray();
    
    console.log(`Found ${sensors.length} sensors:`);
    sensors.forEach((sensor, index) => {
      console.log(`  ${index + 1}. ID: ${sensor._id}, Addr: ${sensor.addr}, Label: ${sensor.label || 'N/A'}`);
    });
    
    return sensors;
  } catch (error) {
    console.error('Error finding sensors:', error.message);
    return [];
  } finally {
    await client.close();
  }
}

async function main() {
  console.log('🚀 Starting sensor setup...\n');
  
  try {
    // Find existing sensors first
    await findExistingSensors();
    
    console.log('\n');
    
    // Create or find test sensor
    const sensor = await createTestSensor();
    
    if (sensor) {
      console.log('\n✨ Sensor setup completed!');
      console.log(`🎯 Test sensor ready: ${sensor.addr} (${sensor._id})`);
      console.log('\n📝 Next steps:');
      console.log('1. Cache data has already been added to DynamoDB');
      console.log('2. You can now test the endpoint with this sensor ID');
      console.log(`3. Use sensor ID: ${sensor._id}`);
    } else {
      console.error('❌ Failed to create test sensor');
    }
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
  }
}

// Run the setup
if (require.main === module) {
  main().catch(console.error);
}
