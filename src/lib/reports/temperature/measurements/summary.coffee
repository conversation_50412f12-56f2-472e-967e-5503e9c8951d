config = require 'config'
knex = require 'knex'
spacetime = require 'spacetime'
_ = require 'lodash'
parallelLimit = require 'async/parallelLimit'

apiUtils = reqlib 'modules/apiUtils'
utils = reqlib 'modules/myUtils'


module.exports = (baseReport) -> class extends baseReport

  run: =>

    @req.setTimeout 180000

    generateResults = (deviceList, cb) =>          

      params = @req.params
      { tz } = utils.prefs @req

      tsPairs = utils.getTsPairs params, tz

      taskList = _.flatten (deviceList.map (device) -> tsPairs.map ({ tsSince, tsUntil }) -> { device, tsSince, tsUntil })
      parallelLimit (taskList.map ({ device, tsSince, tsUntil }) => (cb) =>
        @retrieveSummary @req, @res, device, tsSince, tsUntil, (err, result) -> cb err, [device._id.toString()]: result
      ), 10, (err, deviceSummaryList) =>

        if err
          if err.message is 'RESULT_SET_TOO_LARGE' then @res.statusCode = 504
          @res.json err

        else
          deviceSummaryList = _.merge deviceSummaryList...

          results = []
          for deviceId, buckets of deviceSummaryList

            groupByBuckets = _.reduce buckets, ((acc, summary, bucket) ->
              group = unless params._groupBy? then 'no-group' else utils.toUTCZ (spacetime bucket).goto(tz).startOf params._groupBy #(params._groupBy in ['hour', 'day', 'week'])
              acc[group] ?= []
              acc[group].push summary
              acc
            ), {}

            for group, buckets of groupByBuckets

              result = {}

              for summary in buckets

                if summary.temperature?

                  result.temperature ?=
                    max: measurement: -Infinity
                    min: measurement: Infinity
                    avg: sum: 0, count: 0

                  if summary.temperature.max.measurement > result.temperature.max.measurement
                    result.temperature.max =
                      ts: utils.toUTCZ (spacetime summary.temperature.max.ts).goto tz
                      measurement: summary.temperature.max.measurement

                  if summary.temperature.min.measurement < result.temperature.min.measurement
                    result.temperature.min =
                      ts: utils.toUTCZ (spacetime summary.temperature.min.ts).goto tz
                      measurement: summary.temperature.min.measurement
                
                  result.temperature.avg.sum += summary.temperature.avg * summary.temperature.count or 0
                  result.temperature.avg.count += summary.temperature.count or 0

                if summary.humidity?

                  result.humidity ?=
                    max: measurement: -Infinity
                    min: measurement: Infinity
                    avg: sum: 0, count: 0

                  if summary.humidity.max.measurement > result.humidity.max.measurement
                    result.humidity.max =
                      ts: utils.toUTCZ (spacetime summary.humidity.max.ts).goto tz
                      measurement: summary.humidity.max.measurement

                  if summary.humidity.min.measurement < result.humidity.min.measurement
                    result.humidity.min =
                      ts: utils.toUTCZ (spacetime summary.humidity.min.ts).goto tz
                      measurement: summary.humidity.min.measurement
                
                  result.humidity.avg.sum += summary.humidity.avg * summary.humidity.count or 0
                  result.humidity.avg.count += summary.humidity.count or 0

              result.temperature?.avg = parseFloat ((result.temperature.avg.sum / result.temperature.avg.count) or 0).toFixed 1
              result.humidity?.avg = parseFloat ((result.humidity.avg.sum / result.humidity.avg.count) or 0).toFixed 1

              if params._groupBy?
                result.ts =
                  since: utils.toUTCZ (spacetime new Date (Math.max (new Date group), new Date params['ts.since'])).goto tz
                  until: utils.toUTCZ (spacetime new Date (Math.min (spacetime new Date group).goto(tz).add(1, params._groupBy).add(-1, 'second').toLocalDate(), new Date params['ts.until'])).goto tz

              device = deviceList.find (device) -> device._id.toString() is deviceId

              if result.temperature?
                results.push Object.assign result,
                  if device.addr?
                    temperatureSensor: _.pickBy
                      _id: device._id
                      label: device.label
                      addr: device.addr
                  else
                    vehicle: _.pickBy
                      _id: device._id
                      licensePlate: device.licensePlate
                      imei: device.imei

          results = _.sortBy results, params._sortBy
          if params._sortOrder is 'DESC' then results = results.reverse()
          
          # results = results.map (result) -> _.pick result, params._pull.concat ['vehicle', 'ts']
          @res.setHeader 'X-TRIO-Total-Count', results.length unless @res.headersSent
          results = results.slice ((params._page - 1) * params._perPage), ((params._page - 1) * params._perPage) + params._perPage

          @res.json null, results

    
    if @req.params.vehicleId?

      @getVehicleList pagination: false, (err, vehicleList) =>

        if err or vehicleList?.length is 0
          console.error err if err
          @res.json err

        else
          generateResults vehicleList


    else if @req.params.temperatureSensorId?

      @getTemperatureSensorList pagination: false, (err, temperatureSensorList) =>

        if err or temperatureSensorList?.length is 0
          console.error err if err
          @res.json err

        else
          generateResults temperatureSensorList
