util = require 'util'
_ = require 'lodash'
config = require 'config'
dynogels = require 'dynogels'
mongolayer = require 'mongolayer'
spacetime = require 'spacetime'
parallelLimit = require 'async/parallelLimit'

utils = reqlib 'modules/myUtils'
apiUtils = reqlib 'modules/apiUtils'
ratelimiter = reqlib 'modules/ratelimiter'

TemperatureSummary = dynogels.define config.dynamodb.tables.temperature.summary, hashKey: 'device-identifier', rangeKey: 'ts'
Measurements =
  vehicle: dynogels.define config.dynamodb.tables.vehicle.temperature.measurements, hashKey: 'vehicleId', rangeKey: 'ts'
  temperatureSensor: dynogels.define config.dynamodb.tables.temperature.measurements, hashKey: 'addr', rangeKey: 'ts'

module.exports = class extends reqlib 'reports/_baseReport'

  getTemperatureSensorList: ({ pagination }, cb) ->

    query = {}

    if @req.params.temperatureSensorId?

      if _.isArray @req.params.temperatureSensorId
        query._id = $in: @req.params.temperatureSensorId.map (temperatureSensorId) -> new mongolayer.ObjectId temperatureSensorId
      else
        query._id = new mongolayer.ObjectId @req.params.temperatureSensorId

      delete @req.params.temperatureSensorId

    @mongo.api.models.temperatureSensor.find query
    , hooks: ( @hooks { @req, @res, model: 'temperatureSensor' }, [(if pagination then 'list' else 'get')] ), limit: 2000
    , cb


  getTemperatureSensor: (temperatureSensorId, cb) ->

    @mongo.api.models.temperatureSensor.findById temperatureSensorId
    , hooks: ( @hooks { @req, @res, model: 'temperatureSensor' }, ['get'] )
    , cb


  getVehicleList: ({ pagination }, cb) ->

    query = {}

    if @req.params.vehicleId?

      if _.isArray @req.params.vehicleId
        query._id = $in: @req.params.vehicleId.map (vehicleId) -> new mongolayer.ObjectId vehicleId
      else
        query._id = new mongolayer.ObjectId @req.params.vehicleId

      delete @req.params.vehicleId

    @mongo.api.models.vehicle.find query
    , hooks: ( @hooks { @req, @res, model: 'vehicle' }, [(if pagination then 'list' else 'get')] ), limit: 2000
    , cb


  getVehicle: (vehicleId, cb) ->

    @mongo.api.models.vehicle.findById vehicleId
    , hooks: ( @hooks { @req, @res, model: 'vehicle' }, ['get'] )
    , cb


  getLocation: (req, { lat, lon }) ->

    if lat? and lon?
      unless 'location.address' in _.flatten [(@req.params._pull or [])]
        { lat, lon }
      else
        await utils.withAddress req, { lat, lon }


  retrieveSummary: (req, res, device, tsSince, tsUntil, cb) ->

    if device.addr? #temperatureSensor
      addr = device.addr
    else #vehicle
      vehicleId = device._id.toString()

    console.info "generating temperature summary for #{vehicleId or addr}: #{tsSince.toISOString()} - #{tsUntil.toISOString()}"

    unless await ratelimiter.isRequestThrottled { req, res, key: "dynamodb:temperature:summary:#{vehicleId or addr}", perHour: 200 }

      tsSince = utils.toUTCZ tsSince
      tsUntil = utils.toUTCZ Math.min (new Date tsUntil), new Date

      if new Date < new Date tsSince
        cb null, {}

      else
        cachedSummary = {}
        queriedSummary = {}

        getKeyPairs cachedSummary, (vehicleId or addr), tsSince, tsUntil, (keyPairs) ->

          parallelLimit (keyPairs.map (keyPair) -> (cb) ->
          
            { startKey, endKey } = keyPair
            items = []

            do run = ({ startKey, endKey }) ->

              # console.debug '* do run', { vehicleId, addr, startKey, endKey }

              query = if vehicleId?
                Measurements.vehicle.query vehicleId
                .attributes ['ts', 'temperature', 'humidity']
              else
                Measurements.temperatureSensor.query addr
                .attributes ['ts', 'temperature']

              query.where('ts').between startKey, endKey
              .exec (err, data) ->

                if err then cb err
                # else if data.Items.length > 0 and data.Items.some (item) -> item.attrs.mileage? and item.attrs.ignition? and item.attrs.speed?
                #   cb new Error 'Attributes missing in the result set: `mileage`, `ignition`, `speed`'
                else
                  items.push (data.Items.map (item) -> item.attrs)...

                  if data.LastEvaluatedKey?

                    if items.length < config.reports.temperature.summary.queryLimit
                      run
                        startKey: utils.toUTCZ (new Date data.LastEvaluatedKey.ts).getTime() + 1000
                        endKey: endKey
                    else
                      cb new Error 'RESULT_SET_TOO_LARGE'

                  else
                    # console.info "#{summaryType} summary query #{vehicleId}: #{startKey} - #{endKey} > result: #{items.length} items"
                    if items.length is 0 then do cb
                    else
                      Object.assign queriedSummary, generateSummary keyPair.startKey, endKey, items
                      do cb

          ), 10, (err) ->

            # console.debug '***', { queriedSummary, cachedSummary }
            cb err, Object.assign queriedSummary, cachedSummary

            # cache results in the background
            setTimeout ->

              parallelLimit (({ bucket, bucketItems } for bucket, bucketItems of (_.pickBy queriedSummary, (bucketItems, bucket) -> not cachedSummary[bucket]? and not bucketItems.partial and (spacetime bucket).add(1, 'hour').toLocalDate() < new Date)).map ({ bucket, bucketItems }) -> (cb) ->
                
                delete bucketItems.partial
                ttl = config.reports.temperature.summary.cache.ttl + Math.round Date.now() / 1000

                TemperatureSummary.update (Object.assign bucketItems, #TODO maybe use AWS.DynamoDB.DocumentClient with batchWriteItem
                  'device-identifier': vehicleId or addr
                  ts: bucket
                  ttl: ttl
                ), (err) ->
                  if err then console.error "temperature bucket #{bucket} failed to be cached", err
                  do cb
              ), 10

            , 6000


  generateSummary = (startKey, endKey, items) ->

    startKey = new Date Math.min (new Date startKey).getTime(), (new Date items[0].ts).getTime()
    endKey = new Date Math.max (new Date endKey).getTime(), (new Date items[items.length-1].ts).getTime()

    buckets = {}
    bucket = (spacetime startKey).startOf('hour')
    while bucket.toLocalDate() < new Date endKey #endKey also should not be after current time
      buckets[utils.toUTCZ bucket] = {}
      bucket = bucket.add 1, 'hour'

    for bucket, bucketItems of (_.groupBy items, ({ ts }) -> (ts.substring 0, 14) + '00:00Z') #(spacetime ts).startOf('hour').toLocalDate().toISOString().split('.')[0] + 'Z')

      buckets[bucket] =
        partial: not ((startKey <= new Date bucket) and spacetime(bucket).startOf('hour').add(1, 'hour').add(-1, 'second').toLocalDate() <= endKey)

      temperatureList = []
      humidityList = []

      for { ts, temperature, humidity } in bucketItems

        if temperature?

          temperature = temperature.inside or temperature

          buckets[bucket].temperature ?=
            max: measurement: -Infinity
            min: measurement: Infinity
            avg: 0

          temperatureList.push temperature
          if buckets[bucket].temperature.max.measurement < temperature
            buckets[bucket].temperature.max = { ts, measurement: temperature }
          if buckets[bucket].temperature.min.measurement > temperature
            buckets[bucket].temperature.min = { ts, measurement: temperature }

        if humidity?
          buckets[bucket].humidity ?=
            max: measurement: -Infinity
            min: measurement: Infinity
            avg: 0

          humidityList.push humidity
          if buckets[bucket].humidity.max.measurement < humidity
            buckets[bucket].humidity.max = { ts, measurement: humidity }
          if buckets[bucket].humidity.min.measurement > humidity
            buckets[bucket].humidity.min = { ts, measurement: humidity }

      if temperatureList.length > 0
        buckets[bucket].temperature.count = temperatureList.length
        buckets[bucket].temperature.avg = (temperatureList.reduce (a, b) -> a + b) / temperatureList.length

      if humidityList.length > 0
        buckets[bucket].humidity.count = humidityList.length
        buckets[bucket].humidity.avg = (humidityList.reduce (a, b) -> a + b) / humidityList.length

    # console.debug '*** buckets', JSON.stringify buckets, null, 2
    buckets

  
  getKeyPairs = (cachedSummary, deviceIdentifier, tsSince, tsUntil, cb) ->

    # console.debug '#getKeyPairs', tsSince, tsUntil
    cacheTtl = Math.round (Math.max Date.now(), ((new Date config.reports.temperature.summary.cache.invalidUntil).getTime() + config.reports.temperature.summary.cache.ttl * 1000)) / 1000
    # console.debug {cacheTtl}

    data = try
      await TemperatureSummary.query deviceIdentifier
      .where('ts').between tsSince, utils.toUTCZ (spacetime new Date tsUntil).add(1, 'minute').startOf('hour').add(-1, 'hour')
      .attributes ['ts', 'temperature', 'humidity']
      .filter('ttl').gt cacheTtl
      .execAsync()

    data?.Items.forEach (item) -> cachedSummary[item.attrs.ts] = _.omit item.attrs, ['ts']
    # console.debug "cachedSummary found #{(Object.keys cachedSummary).length} keys"

    keyPairs = []
    # do ->
    startKey = null
    endKey = null

    hour = spacetime new Date tsSince
    while hour.toLocalDate() < new Date tsUntil

      unless cachedSummary[utils.toUTCZ hour]?
        if not startKey?
          startKey = utils.toUTCZ hour
      else if startKey? and not endKey?
        endKey = utils.toUTCZ hour.add -1, 'second'

      if startKey? and endKey?
        keyPairs.push { startKey, endKey }
        startKey = null
        endKey = null

      hour = hour.startOf('hour').add 1, 'hour'

    if startKey? and not endKey?
      endKey = tsUntil
      keyPairs.push { startKey, endKey }

    cb keyPairs
