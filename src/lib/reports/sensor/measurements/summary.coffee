config = require 'config'
spacetime = require 'spacetime'
_ = require 'lodash'
parallelLimit = require 'async/parallelLimit'

apiUtils = reqlib 'modules/apiUtils'
utils = reqlib 'modules/myUtils'
ratelimiter = reqlib 'modules/ratelimiter'
dynogels = require 'dynogels'

SensorSummary = dynogels.define config.dynamodb.tables.sensor.summary, hashKey: 'addr', rangeKey: 'ts'
Measurements = dynogels.define config.dynamodb.tables.sensor.measurements, hashKey: 'addr', rangeKey: 'ts'

module.exports = (baseReport) -> class extends baseReport

  run: ->
    @getSensorList (err, sensorList) =>
      if err or not sensorList?.length
        console.error err if err
        @res.json err
      else
        params = @req.params
        { tz } = utils.prefs @req
                
        parallelLimit (sensorList.map (sensor) => (cb) =>
          @retrieveSensorSummary @req, @res, sensor, params['ts.since'], params['ts.until'], (err, results) ->
            cb err, [sensor._id.toString()]: results
        ), 10, (err, sensorSummaryList) =>
          if err
            if err.message is 'RESULT_SET_TOO_LARGE' then @res.statusCode = 504
            console.error err
            @res.json err
          else
            # console.info 'combined sensor summary results1', sensorSummaryList
            sensorSummaryList = _.assign sensorSummaryList...
            # console.info 'combined sensor summary results2', JSON.stringify sensorSummaryList, null, 2
            
            dateLocale = (date) ->
              date = new Date date
              spacetime [date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds()], tz
            
            formattedResults = []
            
            for sensorId, results of sensorSummaryList
              # console.info 'results', results
              sensor = sensorList.find (s) -> s._id.toString() is sensorId
              if sensor and results
                formattedResults.push @formatResults(results, sensor, params, dateLocale)...
            
            @res.json null, formattedResults

  retrieveSensorSummary: (req, res, sensor, tsSince, tsUntil, cb) ->
    addr = sensor.addr
    
    console.info "generating sensor summary for #{addr}: #{tsSince.toISOString()} - #{tsUntil.toISOString()}"
    
    unless await ratelimiter.isRequestThrottled { req, res, key: "dynamodb:sensor:summary:#{addr}", perHour: 200 }
      tsSince = utils.toUTCZ tsSince
      tsUntil = utils.toUTCZ Math.min (new Date tsUntil), new Date
      
      if new Date < new Date tsSince
        cb null, {}

      else
        cachedSummary = {}
        queriedSummary = {}
        
        @getKeyPairs cachedSummary, addr, tsSince, tsUntil, (keyPairs) =>
          parallelLimit (keyPairs.map (keyPair) => (cb) =>
            { startKey, endKey } = keyPair
            items = []
            
            do run = ({ startKey, endKey }) =>
              Measurements.query addr
                .attributes ['ts'].concat Object.keys(sensor.inputs)
                .where('ts').between startKey, endKey
                .exec (err, data) =>
                  if err
                    cb err
                  else
                    # console.info 'data', data.Items
                    console.info "queried sensor summary for #{addr}: #{startKey} - #{endKey} > result: #{data.Items.length} items"
                    items.push (data.Items.map (item) -> item.attrs)...
                    
                    if data.LastEvaluatedKey?
                      if items.length < config.reports.sensor.summary.queryLimit
                        run
                          startKey: utils.toUTCZ (new Date data.LastEvaluatedKey.ts).getTime() + 1000
                          endKey: endKey
                      else
                        cb new Error 'RESULT_SET_TOO_LARGE'
                    else
                      if items.length is 0 then do cb
                      else
                        Object.assign queriedSummary, @generateSummary keyPair.startKey, endKey, items, sensor
                        do cb
          ), 10, (err) =>
            cb err, Object.assign queriedSummary, cachedSummary
            console.info 'queried sensor summary results', queriedSummary
            console.info 'cached sensor summary results', cachedSummary
            
            setTimeout =>
              parallelLimit (({ bucket, bucketItems } for bucket, bucketItems of (_.pickBy queriedSummary, (bucketItems, bucket) ->
                not cachedSummary[bucket]? and not bucketItems.partial and (spacetime bucket).add(1, 'hour').toLocalDate() < new Date
              )).map ({ bucket, bucketItems }) => (cb) =>
                delete bucketItems.partial
                ttl = config.reports.sensor.summary.cache.ttl + Math.round Date.now() / 1000
                
                SensorSummary.update (Object.assign bucketItems,
                  addr: addr
                  ts: bucket
                  ttl: ttl
                ), (err) ->
                  if err then console.error "sensor bucket #{bucket} failed to be cached", err
                  do cb
              ), 10
            , 6000
            
  getKeyPairs: (cachedSummary, addr, tsSince, tsUntil, cb) ->
    cacheTtl = Math.round (Math.max Date.now(), ((new Date config.reports.sensor.summary.cache.invalidUntil).getTime() + config.reports.sensor.summary.cache.ttl * 1000)) / 1000
    
    data = try
      await SensorSummary.query addr
        .where('ts').between tsSince, utils.toUTCZ (spacetime new Date tsUntil).add(1, 'minute').startOf('hour').add(-1, 'hour')
        .filter('ttl').gt cacheTtl
        .execAsync()
    
    data?.Items.forEach (item) -> cachedSummary[item.attrs.ts] = _.omit item.attrs, ['ts', 'addr', 'ttl']
    
    keyPairs = []
    startKey = null
    endKey = null
    
    hour = spacetime new Date tsSince
    while hour.toLocalDate() < new Date tsUntil
      unless cachedSummary[utils.toUTCZ hour]?
        if not startKey?
          startKey = utils.toUTCZ hour
      else if startKey? and not endKey?
        endKey = utils.toUTCZ hour.add -1, 'second'
      
      if startKey? and endKey?
        keyPairs.push { startKey, endKey }
        startKey = null
        endKey = null
      
      hour = hour.startOf('hour').add 1, 'hour'
    
    if startKey? and not endKey?
      endKey = tsUntil
      keyPairs.push { startKey, endKey }
    
    cb keyPairs
    
  generateSummary: (startKey, endKey, items, sensor) ->
    startKey = new Date Math.min (new Date startKey).getTime(), (new Date items[0].ts).getTime()
    endKey = new Date Math.max (new Date endKey).getTime(), (new Date items[items.length-1].ts).getTime()
    
    buckets = {}
    bucket = (spacetime startKey).startOf('hour')
    while bucket.toLocalDate() < new Date endKey
      buckets[utils.toUTCZ bucket] = {}
      bucket = bucket.add 1, 'hour'
    
    inputList = Object.keys sensor.inputs
    
    for bucket, bucketItems of (_.groupBy items, ({ ts }) -> (ts.substring 0, 14) + '00:00Z')
      buckets[bucket] =
        partial: not ((startKey <= new Date bucket) and spacetime(bucket).startOf('hour').add(1, 'hour').add(-1, 'second').toLocalDate() <= endKey)
      
      for input in inputList
        if input.startsWith 'analog'
          values = bucketItems.map (item) -> item[input]
          values = values.filter (v) -> v?

          if values.length > 0
            buckets[bucket][input] ?=
              max: measurement: -Infinity
              min: measurement: Infinity
              avg: 0
              count: 0

            for item in bucketItems when item[input]?
              value = item[input]
              if buckets[bucket][input].max.measurement < value
                buckets[bucket][input].max = { ts: item.ts, measurement: value }
              if buckets[bucket][input].min.measurement > value
                buckets[bucket][input].min = { ts: item.ts, measurement: value }

            buckets[bucket][input].count = values.length
            buckets[bucket][input].avg = values.reduce((a, b) -> a + b) / values.length
        else
          ones = bucketItems.filter (item) -> item[input] == 1
          zeros = bucketItems.filter (item) -> item[input] == 0

          buckets[bucket][input] ?= {}

          if ones.length > 0
            firstOne = _.minBy ones, 'ts'
            lastOne = _.maxBy ones, 'ts'
            buckets[bucket][input]["1"] =
              count: ones.length
              first: firstOne.ts
              last: lastOne.ts

          if zeros.length > 0
            firstZero = _.minBy zeros, 'ts'
            lastZero = _.maxBy zeros, 'ts'
            buckets[bucket][input]["0"] =
              count: zeros.length
              first: firstZero.ts
              last: lastZero.ts
    
    return buckets
    
  formatResults: (rows, sensor, params, dateLocale) ->
    return [] unless rows?
    
    formattedResults = []
    
    for timestamp, data of rows
      continue if _.isEmpty(data)
      
      result =
        _id: sensor._id
        addr: sensor.addr
        label: sensor.label
        ts:
          since: utils.toUTCZ dateLocale timestamp
          until: utils.toUTCZ dateLocale(timestamp).add(1, 'hour').subtract(1, 'second')
        measurement: {}
      
      if params._groupBy?
        intervalSince = dateLocale timestamp
        intervalSince = spacetime params['ts.since'], tz if intervalSince?.epoch < params['ts.since'].getTime()
        intervalUntil = dateLocale(timestamp).add(1, params._groupBy).subtract(1, 'seconds')
        intervalUntil = spacetime params['ts.until'], tz if intervalUntil?.epoch > params['ts.until'].getTime()
        
        result.ts.since = utils.toUTCZ intervalSince
        result.ts.until = utils.toUTCZ intervalUntil
      
      for input in Object.keys sensor.inputs
        if input.startsWith 'analog'
          if data[input]?
            result.measurement[input] =
              max: parseFloat data[input].max.measurement
              min: parseFloat data[input].min.measurement
              avg: parseFloat data[input].avg
        else if input.startsWith 'digital'
          if data[input]?
            digitalData = {}
            if data[input]["0"]?
              digitalData["0"] =
                count: parseInt(data[input]["0"].count)
                firstOccurrence: utils.toUTCZ(dateLocale(data[input]["0"].first))
                lastOccurrence: utils.toUTCZ(dateLocale(data[input]["0"].last))

            if data[input]["1"]?
              digitalData["1"] =
                count: parseInt(data[input]["1"].count)
                firstOccurrence: utils.toUTCZ(dateLocale(data[input]["1"].first))
                lastOccurrence: utils.toUTCZ(dateLocale(data[input]["1"].last))

            if Object.keys(digitalData).length > 0
              result.measurement[input] = digitalData
      console.info 'result', result
      formattedResults.push result if Object.keys(result.measurement).length > 0
    
    return formattedResults
