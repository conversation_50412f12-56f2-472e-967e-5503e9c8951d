config = require 'config'
knex = require 'knex'
spacetime = require 'spacetime'
_ = require 'lodash'

apiUtils = reqlib 'modules/apiUtils'
utils = reqlib 'modules/myUtils'


module.exports = (baseReport) -> class extends baseReport

  run: ->

    @getTrackerList pagination: false, (err, trackerList) =>

      if err or trackerList?.length is 0
        console.error err if err
        @res.json err

      else

        tz = @req.user.preferences?.tz or config.defaults.user.preferences.tz
        params = @req.params

        if params._groupBy?
          interval = 'since, '
          trunc = "DATE_TRUNC('#{params._groupBy}', CONVERT_TIMEZONE('#{tz}', ts))"
          partitionBy = ", #{trunc} "
          intervalAs = "#{trunc} as since, "
        else
          interval = ''
          partitionBy = ''
          intervalAs = ''

        query = @redshift.select knex.raw 'addr, ' + interval + ' "position.count", "position.first", "position.last"'
        .from ->
          nestedQuery = @select knex.raw 'addr, ' + intervalAs + '
            Count(DISTINCT "ts") AS "position.count",
            Min("ts") AS "position.first",
            Max("ts") AS "position.last"'
        # .where 'stddev100_in', '<', '50'
        # .where 'stddev100_out', '<', '50'
        # .from ->
        #   @select knex.raw 'ts,
        #                addr,
        #                "position",
        #                Stddev("position")
        #                  OVER (
        #                    PARTITION BY addr ROWS BETWEEN CURRENT ROW AND 100 following) AS stddev100_in,
        #                "position",
        #                Stddev("position")
        #                  OVER (
        #                    PARTITION BY addr ROWS BETWEEN CURRENT ROW AND 100 following) AS stddev100_out'
          .from 'indoor_positions'
          .whereBetween 'ts', [ params['ts.since'], params['ts.until'] ]
          .whereIn 'addr', trackerList.map (tracker) -> tracker.addr
          .groupByRaw 'addr ' + partitionBy

          unless params['validHours.start'] is '00:00'
            [h, m] = params['validHours.start'].split(':')
            nestedQuery.where knex.raw "(DATE_PART('hour', CONVERT_TIMEZONE('#{tz}', ts)) > #{parseInt h} OR (DATE_PART('hour', CONVERT_TIMEZONE('#{tz}', ts)) = #{parseInt h} AND DATE_PART('minute', CONVERT_TIMEZONE('#{tz}', ts)) > #{parseInt m}))"

          unless params['validHours.end'] is '23:59'
            [h, m] = params['validHours.end'].split(':')
            nestedQuery.where knex.raw "(DATE_PART('hour', CONVERT_TIMEZONE('#{tz}', ts)) < #{parseInt h} OR (DATE_PART('hour', CONVERT_TIMEZONE('#{tz}', ts)) = #{parseInt h} AND DATE_PART('minute', CONVERT_TIMEZONE('#{tz}', ts)) < #{parseInt m}))"

          nestedQuery

        if params._groupBy?
          query.orderBy 'since', params._sortOrder or 'ASC'
        # if params._sortBy?
        #   query.orderByRaw (params._sortBy.map (sortBy) -> "#{sortBy} #{params._sortOrder or 'ASC'}").join ', '

        dateLocale = (date) -> date = (new Date date); spacetime [date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds()], tz

        apiUtils.executeQuery.redshift { @req, @res, query }, (err, rows) =>

          @res.json err, _.compact rows?.map (row) ->

            tracker = trackerList.find (tracker) -> tracker.addr is row.addr
            
            if params._groupBy?
              intervalSince = dateLocale row.since
              intervalSince = spacetime params['ts.since'], tz if intervalSince.epoch < params['ts.since'].getTime()
              intervalUntil = dateLocale(row.since).add(1, params._groupBy).subtract(1, 'seconds')
              intervalUntil = spacetime params['ts.until'], tz if intervalUntil.epoch > params['ts.until'].getTime()

            tracker:
              _id: tracker._id
              addr: tracker.addr
              label: tracker.label
            ts:
              if params._groupBy?
                since: utils.toUTCZ intervalSince
                until: utils.toUTCZ intervalUntil
            position:
              count: parseInt row['position.count']
              first: utils.toUTCZ (spacetime row['position.first']).goto tz
              last: utils.toUTCZ (spacetime row['position.last']).goto tz
