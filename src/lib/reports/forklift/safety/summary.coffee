config = require 'config'
knex = require 'knex'
spacetime = require 'spacetime'
_ = require 'lodash'
parallel = require 'async/parallel'
parallelLimit = require 'async/parallelLimit'

apiUtils = reqlib 'modules/apiUtils'
utils = reqlib 'modules/myUtils'


module.exports = (baseReport) -> class extends baseReport

  run: =>

    @req.setTimeout 180000

    reportType = if @req.params.forkliftId? then 'forklift'
    else if @req.params.anchorId? then 'anchor'
    else if @req.params.trackerId? then 'tracker'

    parallel
      forkliftList: (cb) => @getForkliftList pagination: false, cb
      anchorList: (cb) => @getAnchorList pagination: false, (err, res) -> cb null, res
      trackerList: (cb) => @getTrackerList pagination: false, (err, res) -> cb null, res

    , (err, { forkliftList, anchorList = [], trackerList = [] }) =>

      if err or forkliftList?.length is 0
        console.error err if err
        @res.json err

      else

        @res.statusCode = 200
        params = @req.params
        tz = @req.user.preferences?.tz or config.defaults.user.preferences.tz
        
        # parallelLimit (forkliftList.map (forklift) => (cb) =>
        #   @retrieveSummary.safety @req, @res, { forklift }, (new Date params['ts.since']), (new Date params['ts.until']), (err, summaryList) ->
        #     cb err, [forklift._id.toString()]: summaryList
        # ), 10, (err, forkliftSummaryList) =>
        
        parallel [
          (cb) =>
            switch reportType
              when 'forklift'
                parallelLimit (forkliftList.map (forklift) => (cb) =>
                  @retrieveSummary.safety @req, @res, { forklift }, (new Date params['ts.since']), (new Date params['ts.until']), (err, summaryList) ->
                    cb err, [forklift._id.toString()]: summaryList
                ), 10, cb
              when 'anchor'
                parallelLimit (anchorList.map (anchor) => (cb) =>
                  @retrieveSummary.safety @req, @res, { anchor }, (new Date params['ts.since']), (new Date params['ts.until']), (err, summaryList) ->
                    cb err, [anchor._id.toString()]: summaryList
                ), 10, cb
              when 'tracker'
                parallelLimit (trackerList.map (tracker) => (cb) =>
                  @retrieveSummary.safety @req, @res, { tracker }, (new Date params['ts.since']), (new Date params['ts.until']), (err, summaryList) ->
                    cb err, [tracker._id.toString()]: summaryList
                ), 10, cb
        ], (err, forkliftSummaryList) =>

          if err
            if err.message is 'RESULT_SET_TOO_LARGE' then @res.statusCode = 504
            @res.json err

          else
            forkliftSummaryList = _.flatten _.compact forkliftSummaryList
            forkliftSummaryList = _.assign forkliftSummaryList...
            # console.debug 'forkliftSummaryList', JSON.stringify forkliftSummaryList, null, 2

            results = []
            for bucketKey, buckets of forkliftSummaryList

              # console.debug '** buckets', buckets
              if params._groupBy is 'interaction'
                groupByBuckets = {}
                for ts, { interaction } of buckets
                  for addr, anchorInteraction of interaction?.anchor
                    groupByBuckets[addr] ?= []
                    groupByBuckets[addr].push interaction: anchor: [addr]: anchorInteraction
                  for addr, trackerInteraction of interaction?.tracker
                    groupByBuckets[addr] ?= []
                    groupByBuckets[addr].push interaction: tracker: [addr]: trackerInteraction
                  for _id, forkliftInteraction of interaction?.forkliftId
                    groupByBuckets[_id] ?= []
                    groupByBuckets[_id].push interaction: forkliftId: [_id]: forkliftInteraction

              else
                groupByBuckets = _.reduce buckets, ((acc, summary, bucket) ->
                  group = unless params._groupBy? then 'no-group' else utils.toUTCZ (spacetime bucket).goto(tz).startOf params._groupBy #(params._groupBy in ['hour', 'day', 'week'])
                  acc[group] ?= []
                  acc[group].push summary
                  acc
                ), {}

              # console.debug 'groupByBuckets', JSON.stringify groupByBuckets, null, 2
              for group, buckets of groupByBuckets

                result = interaction: {}

                for { interaction } in buckets
                  
                  if interaction?.anchor?
                    for addr, item of interaction.anchor
                      for speed, { count, first, last } of item
                        result.interaction.anchor ?= speed: {}
                        result.interaction.anchor.speed[speed] ?= count: 0, first: (new Date), last: (new Date 0)
                        result.interaction.anchor.speed[speed].count += count
                        result.interaction.anchor.speed[speed].first = utils.toUTCZ (spacetime new Date Math.min (new Date result.interaction.anchor.speed[speed].first), (new Date first)).goto tz #unless params._groupBy is 'status'
                        result.interaction.anchor.speed[speed].last = utils.toUTCZ (spacetime new Date Math.max (new Date result.interaction.anchor.speed[speed].last), (new Date last)).goto tz #unless params._groupBy is 'status'
                  
                  if interaction?.tracker?
                    for addr, item of interaction.tracker
                      for speed, { count, first, last, distance } of item
                        result.interaction.tracker ?= speed: {}
                        result.interaction.tracker.speed[speed] ?= count: 0, first: (new Date), last: (new Date 0), distance: avg: 0
                        result.interaction.tracker.speed[speed].count += count
                        result.interaction.tracker.speed[speed].first = utils.toUTCZ (spacetime new Date Math.min (new Date result.interaction.tracker.speed[speed].first), (new Date first)).goto tz #unless params._groupBy is 'status'
                        result.interaction.tracker.speed[speed].last = utils.toUTCZ (spacetime new Date Math.max (new Date result.interaction.tracker.speed[speed].last), (new Date last)).goto tz #unless params._groupBy is 'status'
                        result.interaction.tracker.speed[speed].distance.avg = Math.round ((result.interaction.tracker.speed[speed].distance.avg * (result.interaction.tracker.speed[speed].count - 1)) + distance.avg) / result.interaction.tracker.speed[speed].count

                  if interaction?.forkliftId?
                    for _id, item of interaction.forkliftId
                      for speed, { count, first, last, distance } of item
                        result.interaction.forklift ?= speed: {}
                        result.interaction.forklift.speed[speed] ?= count: 0, first: (new Date), last: (new Date 0) #, distance: avg: 0
                        result.interaction.forklift.speed[speed].count += count
                        result.interaction.forklift.speed[speed].first = utils.toUTCZ (spacetime new Date Math.min (new Date result.interaction.forklift.speed[speed].first), (new Date first)).goto tz #unless params._groupBy is 'status'
                        result.interaction.forklift.speed[speed].last = utils.toUTCZ (spacetime new Date Math.max (new Date result.interaction.forklift.speed[speed].last), (new Date last)).goto tz #unless params._groupBy is 'status'
                        if reportType is 'tracker'
                          result.interaction.forklift.speed[speed].distance ?= avg: 0
                          result.interaction.forklift.speed[speed].distance.avg = Math.round ((result.interaction.forklift.speed[speed].distance.avg * (result.interaction.forklift.speed[speed].count - 1)) + distance.avg) / result.interaction.forklift.speed[speed].count


                if params._groupBy? and params._groupBy isnt 'interaction'
                  result.ts =
                    since: utils.toUTCZ (spacetime new Date (Math.max (new Date group), new Date params['ts.since'])).goto tz
                    until: utils.toUTCZ (spacetime new Date (Math.min (spacetime new Date group).goto(tz).add(1, params._groupBy).add(-1, 'second').toNativeDate(), new Date params['ts.until'])).goto tz

                # console.debug '* result', result
                unless _.isEmpty result.interaction

                  if params._groupBy is 'interaction'
                    type for type, { speed } of result.interaction

                    # console.log 'type', type
                    device = switch type
                      when 'anchor'
                        (_.pick (anchorList.find (anchor) -> anchor.addr is group), ['_id', 'addr', 'label'])# or addr: group
                      when 'tracker'
                        (_.pick (trackerList.find (tracker) -> tracker.addr is group), ['_id', 'addr', 'label'])# or addr: group
                      when 'forklift'
                        (_.pick (forkliftList.find (forklift) -> forklift._id.toString() is group), ['_id', 'imei', 'label'])# or _id: group
                    
                    result = unless _.isEmpty device
                      interaction:
                        [type]: device
                        speed: speed

                  if result?
                    switch reportType
                      when 'forklift'
                        forklift = forkliftList.find (forklift) -> forklift._id.toString() is bucketKey
                        results.push Object.assign result,
                          forklift: _.pickBy
                            _id: forklift._id
                            imei: forklift.imei
                            label: forklift.label
                          # , (item) -> not _.isEmpty item
                      when 'anchor'
                        anchor = anchorList.find (anchor) -> anchor._id.toString() is bucketKey
                        results.push Object.assign result,
                          anchor: _.pickBy
                            _id: anchor._id
                            addr: anchor.addr
                            label: anchor.label
                          # , (item) -> not _.isEmpty item
                      when 'tracker'
                        tracker = trackerList.find (tracker) -> tracker._id.toString() is bucketKey
                        results.push Object.assign result,
                          tracker: _.pickBy
                            _id: tracker._id
                            addr: tracker.addr
                            label: tracker.label
                          # , (item) -> not _.isEmpty item
          
          results = _.sortBy results, (item) ->
            (_.get item, params._sortBy) or
            do ->
              parts = params._sortBy.split '.'
              # console.log {parts, item, parts01: parts[0..1].join('.'), get: (_.without parts, parts[1]).join('.'), partsAlt: _.get item, (_.without parts, parts[1]).join('.')}
              if _.get item, parts[0..1].join('.') then _.get item, (_.without parts, parts[1]).join('.')
              else 0

          if params._sortOrder is 'DESC' then results = results.reverse()

          @res.setHeader 'X-TRIO-Total-Count', results.length
          results = results.slice ((params._page - 1) * params._perPage), ((params._page - 1) * params._perPage) + params._perPage

          @res.json null, results
