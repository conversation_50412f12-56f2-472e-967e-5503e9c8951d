config = require 'config'
mongolayer = require 'mongolayer'
spacetime = require 'spacetime'

module.exports = class Vehicle extends reqlib 'controller/_modelController'

  indexList = [
      keys: imei: 1
      options: background: true
    ,
      keys: serialNo: 1
      options: background: true
    ,
      keys: licensePlate: 1
      options: background: true
    ,
      keys: customerId: 1
      options: background: true
    ,
      keys: dealerId: 1
      options: background: true
    ,
      keys: 'audit.deletedAt': 1, licensePlate: 1
      options: background: true
    ,
      keys: 'audit.deletedAt': 1, imei: 1
      options: background: true, unique: true, partialFilterExpression: imei: $exists: true
    ,
      keys: 'audit.deletedAt': 1, serialNo: 1
      options: background: true, unique: true, partialFilterExpression: serialNo: $exists: true
    ,
      keys: 'audit.deletedAt': 1, gsmNo: 1
      options: background: true, unique: true, partialFilterExpression: gsmNo: $exists: true
    ,
      keys: 'audit.deletedAt': 1, licensePlate: 1, customerId: 1
      options: background: true, unique: true, partialFilterExpression: licensePlate: $exists: true
  ]

  if config.app is 'fleet'

    indexList.push ...[
    #   keys: 'audit.deletedAt': 1, isDisabled: 1, _id: 1 #performance improvement
    #   options: background: true
    # ,
      keys: 'audit.deletedAt': 1, 'audit.createdAt': -1, _id: -1 #performance improvement
      options: background: true
    ,
      keys: 'subscription.period.endDate': 1, 'subscription.status': 1, 'audit.createdAt': -1, _id: -1
      options: background: true
    ,
      keys: 'audit.deletedAt': 1, dealerId: 1, 'subscription.period.endDate': 1, 'subscription.status': 1, 'audit.createdAt': -1, _id: -1
      options: background: true
    ]

  model:
    collection: 'vehicle'
    indexes: indexList


  isCommitmentMet = (req, res, mongo, hooks) ->
    
    method = req.method

    req.method = 'GET'

    vehicle =  await mongo.api.models.vehicle.promises.findById req.params.id
    , hooks: ( hooks { req, res, model: 'vehicle' }, ['read'] )

    req.method = method

    unless vehicle? then false
    else if not vehicle.subscription?.commitment? then true
    else if vehicle.subscription?.status is 'CANCELLED' then true
    else if (method is 'PUT' and req.body.customerId is vehicle.customerId.toString()) then true
    else do ->
      
      { startDate, duration } = vehicle.subscription.commitment

      startDate = spacetime startDate

      durationInYears = switch duration
        when '2_YEAR' then 2
        when '3_YEAR' then 3
        when '4_YEAR' then 4
        when '5_YEAR' then 5

      (startDate.add durationInYears, 'year').toNativeDate() < new Date


  list: (req, res, next) =>

    unless req.params.licensePlate?
      super req, res, next

    else

      licensePlate = req.params.licensePlate
      delete req.params.licensePlate

      @mongo.api.models[@model.collection].find { licensePlate }
      , hooks: ( @hooks { req, res, model: @model.collection }, ['list'] )
      , (err, result) =>
        if result?.length is 1
          res.json err, result
        else
          req.params.licensePlate = licensePlate
          super req, res, next


  update: (req, res, next) =>

    unless await isCommitmentMet req, res, @mongo, @hooks
      res.statusCode = 409
      res.json new Error '`vehicle.customerId` cannot be updated when subscription.commitment is not met'

    else
      super req, res, next


  delete: (req, res, next) =>

    unless await isCommitmentMet req, res, @mongo, @hooks
      res.statusCode = 409
      res.json new Error 'Vehicle cannot be deleted when subscription.commitment is not met'
        
    else
      super req, res, next


  summary: (req, res, next) =>

    pipeline = [
        
      $match: status: $exists: true
    ,
      $unwind: '$status'
    ,
      $group:
        _id: '$status.type'
        count:
          $sum: 1
    ,
      $group:
        _id: null
        status:
          $push:
            type: '$_id'
            count: '$count'
    ,
      $project:
        _id: 0
        status: 1
    ]

    @mongo.api.models.vehicle.aggregate pipeline
      , hooks: ( @hooks { req, res, model: 'vehicle' }, ['list'] )
      , (err, resultList) ->
        res.json err, resultList?[0]
