fs           = require 'fs'
app          = do require 'connect'
parallel     = require 'async/parallel'
AWS          = require 'aws-sdk'
config       = require 'config'
mongolayer   = require 'mongolayer'
swaggerTools = require 'swagger-tools'
knex         = require 'knex'
cors         = require 'cors'
Redis        = require 'ioredis'
_            = require 'lodash'
eachDeep     = require 'deepdash/eachDeep'
{ xss }      = require 'express-xss-sanitizer'
# mongoLogger  = require('mongodb').Logger

AppMiddleware = reqlib 'middleware'
Controller = reqlib 'controller'
apiUtils = reqlib 'modules/apiUtils'
maintenance = reqlib 'modules/maintenance'
emailer = reqlib 'modules/emailer'
ratelimiter = reqlib 'modules/ratelimiter'
geocoding = reqlib 'modules/geocoding'
authUtils = reqlib 'modules/authUtils'

module.exports = (moduleCb) ->

  # mongoLogger.setLevel 'debug' #debug, info, default: error
  parallel

    aws: (cb) -> AWS.config.getCredentials cb

    redshift: (cb) ->

      unless config.redshift? then do cb
      else
        client = knex
          client: 'pg'
          connection: config.redshift
          pool:
            min: 2
            max: 10

        client.select(1)
        .then -> cb null, client
        .catch (err) -> cb err

    # postgres: (cb) ->

    #   unless config.postgres then do cb
    #   else
    #     client = knex
    #       client: 'pg'
    #       connection: config.postgres
    #       pool:
    #         min: 2
    #         max: 10

    #     client.select(1)
    #     .then -> cb null, client
    #     .catch (err) -> cb err

    mongo: (cb) ->

      parallel

        api: (cb) ->

          mongolayer.connect
            connectionString: config.mongodb.api.url
            logger: (log) -> console.info "api@#{log?.collection}->#{log?.type} (#{Math.round log?.timers?.command} ms): #{unless log?.args?.rawFilter? then '[rawFilter missing]' else _.truncate (JSON.stringify log.args.rawFilter), length: 500}" ##{JSON.stringify log?.args?.rawFilter, null, 2}"
          , cb

        alarm: (cb) ->

          mongolayer.connect
            connectionString: config.mongodb.alarm.url
            logger: (log) -> console.info "alarm@#{log?.collection}->#{log?.type} (#{Math.round log?.timers?.command} ms): #{unless log?.args?.rawFilter? then '[rawFilter missing]' else _.truncate (JSON.stringify log.args.rawFilter), length: 500}" ##{JSON.stringify log?.args?.rawFilter, null, 2}"
          , cb

      , (err, conn) ->
        
        cb err, conn
        # unless err
        #   for name, { db } of conn
        #     db.on 'connect', -> console.info "mongodb.#{name} event `connect`"
        #     db.on 'authenticated', -> console.info "mongodb.#{name} event `authenticated`"
        #     db.on 'close', (err) -> console.warn "mongodb.#{name} event `close`", err
        #     db.on 'error', (err) -> console.error "mongodb.#{name} event `error`", err
        #     # db.on 'fullsetup', -> console.info "mongodb.#{name} event `fullsetup`"
        #     db.on 'timeout', (err) -> console.error "mongodb.#{name} event `timeout`", err
        #     db.on 'reconnect', -> console.info "mongodb.#{name} event `reconnect`"
        #     db.on 'parseError', (err) -> console.error "mongodb.#{name} event `parseError`", err

    redis: (cb) ->

      client = {}

      eachDeep config.redis, (value, key, parentValue, context) ->
        if parentValue.url? and not (_.get client, context.parent.path)?
          _.set client, context.parent.path, (new Redis parentValue.url, keyPrefix: parentValue.prefix).on 'error', (err) ->
            console.error "redis (#{context.parent.path})", err.message or err
            throw new Error err
      , leavesOnly: true

      # client.on 'error', (err) -> throw new Error err
      # client.on 'ready', -> cb false, client
      cb null, client


  , (err, { mongo, redis, redshift, postgres }) ->

    throw new Error err if err
    console.info 'DB connections are ready.'

    apiUtils.init mongo, redis, redshift, postgres
    authUtils.init mongo, redis
    emailer.init mongo, redis
    ratelimiter.init redis
    geocoding.init redis, postgres

    swagger = require "../api/#{config.app}/#{config.version}/swagger.json"
    swaggerTools.initializeMiddleware swagger, (swaggerMiddleware) ->

      app.use AppMiddleware.logger
      app.use AppMiddleware.qsExceptionHandler
      app.use AppMiddleware.timeout
      app.use AppMiddleware.formDataConfig
      app.use AppMiddleware.jsonResponse
      app.use swaggerMiddleware.swaggerMetadata()
      app.use do xss
      app.use AppMiddleware.qsFilter
      app.use AppMiddleware.formDataFilter
      app.use cors
        origin: config.cors.origin
        allowedHeaders: ['Content-Type', 'Authorization', 'X-TRIO-TOKEN-TTL', 'X-TRIO-FIREBASE-TOKEN', 'X-TRIO-NOTIFY-USER', 'X-TRIO-ID', 'X-TRIO-MIN-TS-DIFF', 'X-TRIO-OTP']
        exposedHeaders: ['X-TRIO-Total-Count', 'X-TRIO-Result-Set-Too-Large', 'X-TRIO-Result-Set-Has-Next-Page', 'X-TRIO-Approximate-Result', 'X-TRIO-NOTICE', 'X-TRIO-2FA-REQUIRED']
      app.use AppMiddleware.paramSwitch
      app.use AppMiddleware.cache
      app.use AppMiddleware.authorization().bearerTokenHandler
      app.use swaggerMiddleware.swaggerSecurity AppMiddleware.authorization redis.session
      app.use AppMiddleware.authorization().errHandler
      app.use swaggerMiddleware.swaggerValidator validateResponse: true
      app.use AppMiddleware.validatorHandler
      app.use swaggerMiddleware.swaggerRouter controllers: (new Controller mongo, redis, redshift, postgres, swagger).handlers, ignoreMissingHandlers: false #config.swagger.router.options
      app.use AppMiddleware.errorHandler

      setTimeout -> #let controller to be initialized for a shortwhile
        moduleCb app
        maintenance.init mongo, redis
      , 3000
