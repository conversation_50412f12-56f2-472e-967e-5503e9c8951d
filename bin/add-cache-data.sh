#!/bin/bash

# Script to add test data to DynamoDB sensor summary cache table
set -e

# Configuration
export AWS_PROFILE=facility
TABLE_NAME="facility-sensor-summary-dev"
SENSOR_ADDR="TEST_SENSOR_001"

echo "🚀 Adding test data to DynamoDB cache table: $TABLE_NAME"
echo "📍 Using sensor address: $SENSOR_ADDR"

# Calculate timestamps for the last 6 hours
current_time=$(date -u +%s)
ttl_time=$((current_time + 90 * 24 * 60 * 60))  # 90 days TTL

echo "⏰ Current time: $(date -u -r $current_time '+%Y-%m-%dT%H:%M:%SZ')"
echo "⏰ TTL time: $(date -u -r $ttl_time '+%Y-%m-%dT%H:%M:%SZ')"

# Function to add cache entry
add_cache_entry() {
    local hour_offset=$1
    local timestamp_seconds=$((current_time - hour_offset * 3600))
    
    # Round to hour start
    local hour_start=$((timestamp_seconds - timestamp_seconds % 3600))
    local iso_timestamp=$(date -u -r $hour_start '+%Y-%m-%dT%H:%M:%SZ')
    
    echo "📝 Adding cache entry for: $iso_timestamp"
    
    # Create JSON item for DynamoDB
    local json_item=$(cat <<EOF
{
    "addr": {"S": "$SENSOR_ADDR"},
    "ts": {"S": "$iso_timestamp"},
    "ttl": {"N": "$ttl_time"},
    "analog1": {
        "M": {
            "max": {
                "M": {
                    "measurement": {"N": "$(echo "8.5 + $RANDOM / 32767 * 1.5" | bc -l)"}
                }
            },
            "min": {
                "M": {
                    "measurement": {"N": "$(echo "2.0 + $RANDOM / 32767 * 1.0" | bc -l)"}
                }
            },
            "avg": {"N": "$(echo "5.5 + $RANDOM / 32767 * 2.0" | bc -l)"}
        }
    },
    "digital1": {
        "M": {
            "0": {
                "M": {
                    "count": {"N": "$((RANDOM % 100))"},
                    "first": {"S": "$(date -u -r $((hour_start + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"},
                    "last": {"S": "$(date -u -r $((hour_start + 1800 + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"}
                }
            },
            "1": {
                "M": {
                    "count": {"N": "$((RANDOM % 50))"},
                    "first": {"S": "$(date -u -r $((hour_start + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"},
                    "last": {"S": "$(date -u -r $((hour_start + 1800 + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"}
                }
            }
        }
    }
}
EOF
)
    
    # Add item to DynamoDB
    if aws dynamodb put-item \
        --table-name "$TABLE_NAME" \
        --item "$json_item" \
        --region us-east-1; then
        echo "  ✅ Successfully added cache entry for $iso_timestamp"
    else
        echo "  ❌ Failed to add cache entry for $iso_timestamp"
    fi
}

# Add cache entries for the last 6 hours
echo ""
for hour in {5..0}; do
    add_cache_entry $hour
    sleep 1  # Small delay to avoid rate limiting
done

echo ""
echo "✨ Finished adding cache data!"
echo ""
echo "🔍 Verifying data in DynamoDB..."

# Query the data we just added
aws dynamodb query \
    --table-name "$TABLE_NAME" \
    --key-condition-expression "addr = :addr" \
    --expression-attribute-values '{":addr":{"S":"'$SENSOR_ADDR'"}}' \
    --region us-east-1 \
    --output table

echo ""
echo "📊 Cache data has been added successfully!"
echo "🧪 You can now test the endpoint with sensor address: $SENSOR_ADDR"
