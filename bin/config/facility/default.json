{"app": "facility", "pagination": {"perPage": 20, "maxSize": 2097152, "limit": 10000}, "formData": {"records": {"maxFileSize": 5242880, "mimetypesAllowed": ["image/png", "image/jpeg", "image/gif", "image/heic", "image/heif", "image/tiff", "application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation"], "extenstionsAllowed": [".png", ".jpg", ".jpeg", ".gif", ".heic", ".heif", ".tiff", ".tif", ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"]}, "floorPlans": {"maxFileSize": 1048576, "mimetypesAllowed": ["image/png", "image/jpeg", "image/gif"], "extenstionsAllowed": [".png", ".jpg", ".jpeg", ".gif"]}, "logo": {"maxFileSize": 1048576, "mimetypesAllowed": ["image/png", "image/jpeg", "image/gif"], "extenstionsAllowed": [".png", ".jpg", ".jpeg", ".gif"]}}, "dynamodb": {"query": {"limit": {"default": 1000, "max": 5000}}}, "s3": {"floorPlans": {"bucket": "facility-api-static", "prefix": "indoor/floorPlans/", "origin": "https://api-static.triomobil.com/", "dimensions": {"max": 6000, "min": 300}}, "logo": {"bucket": "facility-api-static", "prefix": "logo/custom/", "origin": "https://api-static.triomobil.com/", "dimensions": {"max": 640, "min": 64}}}, "fingerprint": {"maxBeacons": 6}, "subscription": {"gracePeriod": 2592000000}, "reports": {"alarm": {"history": {"searchLimit": 500}}, "maxInterval": {"long": {"months": 6}, "medium": {"months": 3}, "short": {"months": 1}, "tiny": {"months": 0.25}}, "dynamix": {"summary": {"queryLimit": 100000, "cache": {"ttl": 7776000}}}, "driving": {"safety": {"summary": {"queryLimit": 100000}, "threshold": {"lateralAcceleration": 350, "harshBraking": 450, "rapidAcceleration": 350}, "limits": {"speeding": 0.2, "rapidAcceleration": 60, "harshBraking": 50, "lateralAcceleration": 20}, "weightingFactor": {"speedingFrequency": 0.5, "positiveDeltaSpeed": 0.1, "rapidAccelerationFrequency": 0.1, "harshBrakingFrequency": 0.2, "lateralAccelerationFrequency": 0.1, "avgFreeSpeed": 0}}, "summary": {"queryLimit": 250000, "cache": {"ttl": 7776000, "invalidUntil": "2025-05-06T16:30:00Z"}}}, "trueai": {"summary": {"queryLimit": 250000, "cache": {"ttl": 7776000, "invalidUntil": "2025-04-28T14:30:00Z"}}}, "temperature": {"summary": {"queryLimit": 250000, "cache": {"ttl": 7776000, "invalidUntil": "2024-11-12T00:00:00Z"}}}, "sensor": {"summary": {"queryLimit": 250000, "cache": {"ttl": 7776000, "invalidUntil": "2025-06-04T00:00:00Z"}}}, "indoor": {"events": {"summary": {"maxEventGap": 210, "minStatinaryPeriod": 120, "queryLimit": 250000, "cache": {"ttl": 7776000, "invalidUntil": "2022-06-20T19:30:00Z"}}}, "zone": {"presence": {"queryLimit": 250000}}}, "forklift": {"movement": {"summary": {"maxEventGap": 120, "minStatinaryPeriod": 60, "queryLimit": 100000, "cache": {"ttl": 7776000, "invalidUntil": "2023-06-16T00:00:00Z"}}}, "safety": {"summary": {"queryLimit": 100000, "cache": {"ttl": 7776000, "invalidUntil": "2024-04-01T00:00:00Z"}}}}}, "restrictedFields": {"customer": {"attrs.driverScoring.isActive": true}}, "redis": {"attributes": {"ttl": {"default": 2592000}}}, "notification": {"ttl": {"max": 2592000}}, "session": {"maxTokens": 20, "ttl": {"resetPassword": 7200, "default": 172800, "max": 2592000}, "ban": {"maxAttempts": 10, "period": 300}}, "command": {"ttl": 604800}, "login": {"initialPasswordChangePrompt": 1296000, "restriction": {"latestLoginAt": 5184000, "latestPasswordChangedAt": {"warning": 6480000}}}, "records": {"maxNumberOfFiles": 10}, "defaults": {"alarmRule": {"state": "OK"}, "energyMeter": {"reactiveAlert": true}, "tracker": {"marker": {"default": {"icon": "circle", "color": "blue"}, "warning": {"icon": "circle", "color": "orange"}, "alert": {"icon": "circle", "color": "red"}}}, "zone": {"color": {"default": "blue", "warning": "orange", "alert": "red"}}, "vehicle": {"profile": "car", "marker": {"icon": "circle", "color": "blue"}}, "poi": {"marker": {"icon": "star", "color": "purple"}}, "trueaiDevice": {"attrs": {"isVideoUploadEnabled": false, "isProvisioningEnabled": false}}, "forklift": {"attrs": {"vibrationThreshold": {"moving": 150}}}, "command": {"status": "PENDING"}, "user": {"preferences": {"locale": "tr-TR", "tz": "Europe/Istanbul", "temperature": "°C", "unitSystem": "Metric", "notifications": {"enabled": true, "type": "in-app"}}}}, "models": {"withTag": ["vehicle", "sensor", "temperatureSensor", "forklift", "trueaiDevice"], "withParent": ["sensor", "tracker", "floorPlan", "fingerprint", "anchor", "peopleCounter", "energyMeter", "temperatureSensor", "sensorCounter", "votemeDevice", "zone", "dynamixDevice", "registerMap", "vehicle", "trueaiDevice", "forklift", "region", "poi", "rfidCard", "record", "reminder"], "latestMonitored": ["gateway", "sensor", "tracker", "anchor", "peopleCounter", "energyMeter", "temperatureSensor", "sensorCounter", "votemeDevice", "dynamixDevice", "vehicle", "trueaiDevice", "forklift", "rfidCard"], "alarmMonitored": ["tracker", "sensor", "temperatureSensor", "energyMeter", "dynamixDevice", "vehicle", "forklift"]}, "ses": {"default": {"from": "Dijital Tesis <<EMAIL>>", "replyto": "<EMAIL>"}}, "alarm": {"sms": {"allowedCountryCodes": ["AL", "AT", "BE", "BA", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IS", "IE", "IT", "LV", "LT", "LU", "MK", "MT", "MD", "MC", "ME", "NL", "NO", "PL", "PT", "RO", "RS", "SK", "SI", "ES", "SE", "CH", "TR", "GB", "US", "MX", "CA"]}}, "swagger": {"paths": {"excluded": ["/reports/vehicle/fuel/history", "/reports/vehicle/canbus/history", "/reports/vehicle/canbus/latest", "/reports/vehicle/canbus/summary", "/reports/vehicle/driving/behavior", "/reports/indoor/outage/history", "/reports/indoor/outage/summary", "/{model}/{id}/subscription"]}, "models": {"excluded": ["alarm-rule-vehicle-fuel-level-delta", "canbus", "identifier-number", "identifierNumber", "subscription", "subscription.interval", "subscription.period.startDate.since", "subscription.period.startDate.until", "subscription.period.endDate.since", "subscription.period.endDate.until", "subscription.commitment.duration", "subscription.commitment.startDate.since", "subscription.commitment.startDate.until", "subscription.addons.hardware", "subscription.addons.connectivity", "subscription.status", "vehicle-fuel-level-measurement"]}}}