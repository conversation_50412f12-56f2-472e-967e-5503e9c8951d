#!/bin/bash

# Script to update cache data for the correct sensor ID
set -e

# Configuration
export AWS_PROFILE=facility
TABLE_NAME="facility-sensor-summary-dev"
OLD_SENSOR_ADDR="TEST_SENSOR_001"
NEW_SENSOR_ADDR="SENSOR_5C9A3F8B"  # We'll use a generic address for the existing sensor

echo "🔄 Updating cache data for correct sensor..."
echo "📍 Correct Sensor ID: 5c9a3f8bb8f3cc2ff71ebd88"
echo "🗑️  Removing old cache data for: $OLD_SENSOR_ADDR"
echo "➕ Adding new cache data for: $NEW_SENSOR_ADDR"

# Step 1: Remove old cache entries
echo ""
echo "🗑️  Removing old cache entries..."

# Query old entries first to see what we're deleting
echo "Checking existing entries for $OLD_SENSOR_ADDR..."
aws dynamodb query \
    --table-name "$TABLE_NAME" \
    --key-condition-expression "addr = :addr" \
    --expression-attribute-values '{":addr":{"S":"'$OLD_SENSOR_ADDR'"}}' \
    --region us-east-1 \
    --output table

# Delete old entries
echo ""
echo "Deleting old entries..."
old_entries=$(aws dynamodb query \
    --table-name "$TABLE_NAME" \
    --key-condition-expression "addr = :addr" \
    --expression-attribute-values '{":addr":{"S":"'$OLD_SENSOR_ADDR'"}}' \
    --region us-east-1 \
    --output json)

echo "$old_entries" | jq -r '.Items[] | "\(.addr.S) \(.ts.S)"' | while read addr ts; do
    if [ ! -z "$addr" ] && [ ! -z "$ts" ]; then
        echo "  Deleting: $addr at $ts"
        aws dynamodb delete-item \
            --table-name "$TABLE_NAME" \
            --key '{"addr":{"S":"'$addr'"},"ts":{"S":"'$ts'"}}' \
            --region us-east-1 > /dev/null
    fi
done

echo "✅ Old cache entries removed"

# Step 2: Add new cache entries for the correct sensor
echo ""
echo "➕ Adding new cache entries for sensor 5c9a3f8bb8f3cc2ff71ebd88..."

# Calculate timestamps for the last 6 hours
current_time=$(date -u +%s)
ttl_time=$((current_time + 90 * 24 * 60 * 60))  # 90 days TTL

# Function to add cache entry
add_cache_entry() {
    local hour_offset=$1
    local timestamp_seconds=$((current_time - hour_offset * 3600))
    
    # Round to hour start
    local hour_start=$((timestamp_seconds - timestamp_seconds % 3600))
    local iso_timestamp=$(date -u -r $hour_start '+%Y-%m-%dT%H:%M:%SZ')
    
    echo "📝 Adding cache entry for: $iso_timestamp"
    
    # Create JSON item for DynamoDB
    local json_item=$(cat <<EOF
{
    "addr": {"S": "$NEW_SENSOR_ADDR"},
    "ts": {"S": "$iso_timestamp"},
    "ttl": {"N": "$ttl_time"},
    "analog1": {
        "M": {
            "max": {
                "M": {
                    "measurement": {"N": "$(echo "8.5 + $RANDOM / 32767 * 1.5" | bc -l)"}
                }
            },
            "min": {
                "M": {
                    "measurement": {"N": "$(echo "2.0 + $RANDOM / 32767 * 1.0" | bc -l)"}
                }
            },
            "avg": {"N": "$(echo "5.5 + $RANDOM / 32767 * 2.0" | bc -l)"}
        }
    },
    "digital1": {
        "M": {
            "0": {
                "M": {
                    "count": {"N": "$((RANDOM % 100))"},
                    "first": {"S": "$(date -u -r $((hour_start + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"},
                    "last": {"S": "$(date -u -r $((hour_start + 1800 + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"}
                }
            },
            "1": {
                "M": {
                    "count": {"N": "$((RANDOM % 50))"},
                    "first": {"S": "$(date -u -r $((hour_start + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"},
                    "last": {"S": "$(date -u -r $((hour_start + 1800 + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')"}
                }
            }
        }
    }
}
EOF
)
    
    # Add item to DynamoDB
    if aws dynamodb put-item \
        --table-name "$TABLE_NAME" \
        --item "$json_item" \
        --region us-east-1; then
        echo "  ✅ Successfully added cache entry for $iso_timestamp"
    else
        echo "  ❌ Failed to add cache entry for $iso_timestamp"
    fi
}

# Add cache entries for the last 6 hours
for hour in {5..0}; do
    add_cache_entry $hour
    sleep 1
done

echo ""
echo "🔍 Verifying new cache data..."

# Query the new data
aws dynamodb query \
    --table-name "$TABLE_NAME" \
    --key-condition-expression "addr = :addr" \
    --expression-attribute-values '{":addr":{"S":"'$NEW_SENSOR_ADDR'"}}' \
    --region us-east-1 \
    --output table

echo ""
echo "✅ Cache data updated successfully!"
echo "📊 New cache data is ready for sensor ID: 5c9a3f8bb8f3cc2ff71ebd88"
echo "🏷️  Cache entries use address: $NEW_SENSOR_ADDR"
echo ""
echo "⚠️  NOTE: You may need to update the sensor record in MongoDB to use address '$NEW_SENSOR_ADDR'"
echo "   OR update this script to use the actual address of sensor 5c9a3f8bb8f3cc2ff71ebd88"
