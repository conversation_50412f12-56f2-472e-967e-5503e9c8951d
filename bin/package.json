{"name": "facility-api", "version": "3.0.0", "description": "Trio Mobil IoT Platform API", "author": "Celal Barış Özdemir", "license": "UNLICENSED", "private": true, "engines": {"node": "20.18.3"}, "scripts": {"start": "node index.js", "postinstall": "node-prune"}, "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.1", "async": "^3.2.6", "aws-sdk": "^2.1692.0", "check-password-strength": "^3.0.0", "clone-deep": "^4.0.1", "combinations": "https://github.com/GCastilho/combinations/tarball/968da60567ca3c70e000d55c99bc53ba81f0b511", "config": "^3.3.12", "connect": "^3.7.0", "console-stamp": "^3.1.2", "cors": "^2.8.5", "crypto-random-string": "^3.3.1", "cryptr": "^6.3.0", "deep-compact": "^1.1.0", "deepdash": "^5.3.9", "deepmerge": "^4.3.1", "dynogels": "^9.1.0", "dynogels-promisified": "^1.0.4", "email-validator": "^2.0.4", "escape-string-regexp": "^4.0.0", "expr-eval": "^2.0.2", "express-timeout-handler": "^2.2.2", "express-xss-sanitizer": "^2.0.0", "extend": "^3.0.2", "extract-stack": "^2.0.0", "fs-readdir-recursive": "^1.1.0", "generate-password": "^1.7.1", "google-auth-library": "^9.15.1", "handlebars": "^4.7.8", "html-to-text": "^9.0.5", "image-size": "^2.0.2", "ioredis": "^5.6.1", "js-deep-equals": "^2.1.1", "jsondiffpatch": "^0.5.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.2.0", "jwt-simple": "^0.5.6", "knex": "^0.21.19", "lodash": "^4.17.21", "lru-cache": "^11.1.0", "mongodb": "^6.16.0", "mongolayer": "^3.0.0", "morgan": "^1.10.0", "node-cron": "^3.0.3", "object-keys-modifier": "^1.4.0", "otpauth": "^9.4.0", "p-iteration": "^1.1.8", "parse-duration": "^1.1.0", "pg": "^8.15.6", "phone": "^3.1.59", "point-in-polygon": "^1.1.0", "rate-limiter-flexible": "^7.1.0", "safe-set-header": "^1.0.0", "spacetime": "^7.10.0", "sprintf-js": "^1.1.3", "statuses": "^2.0.1", "swagger-tools": "https://github.com/triomobil/swagger-tools/tarball/8bbf87e4c48c832724a028dbe59c36d1c54d07dc", "uuid": "^11.1.0"}}