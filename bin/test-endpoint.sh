#!/bin/bash

# Script to test the sensor measurements summary endpoint
set -e

# Configuration
API_BASE_URL="http://localhost:8008/facility/dev"
AUTH_TOKEN="4d76ba36-f0e4-4aff-8022-8b0c97516138"
SENSOR_ID="684a94ed4f70340a7b3f8658"  # The test sensor we created

echo "🧪 Testing sensor measurements summary endpoint..."
echo "📍 Sensor ID: $SENSOR_ID"
echo "🔗 API Base URL: $API_BASE_URL"

# Calculate time range (last 8 hours to cover our cache data)
current_time=$(date -u +%s)
since_time=$((current_time - 8 * 3600))  # 8 hours ago
until_time=$((current_time + 3600))      # 1 hour from now

since_iso=$(date -u -r $since_time '+%Y-%m-%dT%H:%M:%SZ')
until_iso=$(date -u -r $until_time '+%Y-%m-%dT%H:%M:%SZ')

echo "⏰ Time range: $since_iso to $until_iso"

# Build the URL with query parameters
endpoint_url="${API_BASE_URL}/reports/sensor/measurements/summary"
query_params="sensorId=${SENSOR_ID}&ts.since=${since_iso}&ts.until=${until_iso}"
full_url="${endpoint_url}?${query_params}"

echo ""
echo "🌐 Full URL: $full_url"
echo ""

# Test if server is running
echo "🔍 Checking if API server is running..."
if curl -s -f -m 5 "${API_BASE_URL}/util/health" > /dev/null 2>&1; then
    echo "✅ API server is running"
else
    echo "❌ API server is not running or not responding"
    echo "💡 Please start the API server first with: NODE_ENV=development npm start"
    exit 1
fi

echo ""
echo "📡 Making API request..."

# Make the API request
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}\nTIME_TOTAL:%{time_total}" \
    -H "Authorization: $AUTH_TOKEN" \
    -H "Content-Type: application/json" \
    "$full_url")

# Extract HTTP status and response body
http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
time_total=$(echo "$response" | grep "TIME_TOTAL:" | cut -d: -f2)
response_body=$(echo "$response" | sed '/HTTP_STATUS:/,$d')

echo "📊 Response Status: $http_status"
echo "⏱️  Response Time: ${time_total}s"
echo ""

if [ "$http_status" = "200" ]; then
    echo "✅ Success! API returned data:"
    echo "📄 Response Body:"
    echo "$response_body" | jq '.' 2>/dev/null || echo "$response_body"
    
    # Count the number of results
    result_count=$(echo "$response_body" | jq 'length' 2>/dev/null || echo "unknown")
    echo ""
    echo "📈 Summary:"
    echo "  - Number of measurement summaries: $result_count"
    
    if [ "$result_count" != "null" ] && [ "$result_count" != "0" ] && [ "$result_count" != "unknown" ]; then
        echo "🎉 SUCCESS: Cache data was found and returned by the endpoint!"
        echo "✨ The cached DynamoDB data is being properly counted and served."
    else
        echo "⚠️  No measurement summaries returned. This could mean:"
        echo "   - Cache data doesn't match the time range"
        echo "   - Sensor configuration issue"
        echo "   - Cache TTL expired"
    fi
    
elif [ "$http_status" = "401" ]; then
    echo "❌ Authentication failed (401)"
    echo "💡 Check the authorization token"
    
elif [ "$http_status" = "404" ]; then
    echo "❌ Endpoint not found (404)"
    echo "💡 Check the API URL and endpoint path"
    
elif [ "$http_status" = "400" ]; then
    echo "❌ Bad request (400)"
    echo "💡 Check the request parameters"
    echo "📄 Response:"
    echo "$response_body"
    
else
    echo "❌ Request failed with status: $http_status"
    echo "📄 Response:"
    echo "$response_body"
fi

echo ""
echo "🔍 Additional debugging info:"
echo "  - Cache table: facility-sensor-summary-dev"
echo "  - Sensor address: TEST_SENSOR_001"
echo "  - Cache entries should exist for timestamps around: $(date -u -r $((current_time - 6 * 3600)) '+%Y-%m-%dT%H:00:00Z') to $(date -u -r $((current_time - 1 * 3600)) '+%Y-%m-%dT%H:00:00Z')"

echo ""
echo "✨ Test completed!"
