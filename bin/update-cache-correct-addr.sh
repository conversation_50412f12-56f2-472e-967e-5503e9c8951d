#!/bin/bash

# Script to update cache data with the correct sensor address
set -e

# Configuration
export AWS_PROFILE=facility
TABLE_NAME="facility-sensor-summary-dev"
OLD_SENSOR_ADDR="TEST_SENSOR_001"
CORRECT_SENSOR_ADDR="23BCAE"  # Correct address for sensor 5c9a3f8bb8f3cc2ff71ebd88

echo "🔄 Updating cache data with correct sensor address..."
echo "📍 Sensor ID: 5c9a3f8bb8f3cc2ff71ebd88"
echo "🏷️  Correct Address: $CORRECT_SENSOR_ADDR"
echo "🗑️  Removing old cache data for: $OLD_SENSOR_ADDR"

# Step 1: Remove old cache entries
echo ""
echo "🗑️  Removing old cache entries..."

# Get old entries and delete them
old_entries=$(aws dynamodb query \
    --table-name "$TABLE_NAME" \
    --key-condition-expression "addr = :addr" \
    --expression-attribute-values '{":addr":{"S":"'$OLD_SENSOR_ADDR'"}}' \
    --region us-east-1 \
    --output json 2>/dev/null || echo '{"Items":[]}')

echo "$old_entries" | jq -r '.Items[]? | "\(.addr.S) \(.ts.S)"' | while IFS=' ' read -r addr ts; do
    if [ ! -z "$addr" ] && [ ! -z "$ts" ]; then
        echo "  Deleting: $addr at $ts"
        aws dynamodb delete-item \
            --table-name "$TABLE_NAME" \
            --key '{"addr":{"S":"'$addr'"},"ts":{"S":"'$ts'"}}' \
            --region us-east-1 > /dev/null 2>&1
    fi
done

echo "✅ Old cache entries cleaned up"

# Step 2: Add new cache entries with correct address
echo ""
echo "➕ Adding cache entries for sensor address: $CORRECT_SENSOR_ADDR"

# Calculate timestamps for the last 6 hours
current_time=$(date -u +%s)
ttl_time=$((current_time + 90 * 24 * 60 * 60))  # 90 days TTL

# Function to add cache entry
add_cache_entry() {
    local hour_offset=$1
    local timestamp_seconds=$((current_time - hour_offset * 3600))
    
    # Round to hour start
    local hour_start=$((timestamp_seconds - timestamp_seconds % 3600))
    local iso_timestamp=$(date -u -r $hour_start '+%Y-%m-%dT%H:%M:%SZ')
    
    echo "📝 Adding cache entry for: $iso_timestamp"
    
    # Generate random values for measurements
    local analog_max=$(echo "8.5 + $RANDOM / 32767 * 1.5" | bc -l)
    local analog_min=$(echo "2.0 + $RANDOM / 32767 * 1.0" | bc -l)
    local analog_avg=$(echo "5.5 + $RANDOM / 32767 * 2.0" | bc -l)
    local digital_count_0=$((RANDOM % 100))
    local digital_count_1=$((RANDOM % 50))
    local first_time_0=$(date -u -r $((hour_start + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')
    local last_time_0=$(date -u -r $((hour_start + 1800 + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')
    local first_time_1=$(date -u -r $((hour_start + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')
    local last_time_1=$(date -u -r $((hour_start + 1800 + RANDOM % 1800)) '+%Y-%m-%dT%H:%M:%SZ')
    
    # Create JSON item for DynamoDB
    local json_item=$(cat <<EOF
{
    "addr": {"S": "$CORRECT_SENSOR_ADDR"},
    "ts": {"S": "$iso_timestamp"},
    "ttl": {"N": "$ttl_time"},
    "analog1": {
        "M": {
            "max": {
                "M": {
                    "measurement": {"N": "$analog_max"}
                }
            },
            "min": {
                "M": {
                    "measurement": {"N": "$analog_min"}
                }
            },
            "avg": {"N": "$analog_avg"}
        }
    },
    "digital1": {
        "M": {
            "0": {
                "M": {
                    "count": {"N": "$digital_count_0"},
                    "first": {"S": "$first_time_0"},
                    "last": {"S": "$last_time_0"}
                }
            },
            "1": {
                "M": {
                    "count": {"N": "$digital_count_1"},
                    "first": {"S": "$first_time_1"},
                    "last": {"S": "$last_time_1"}
                }
            }
        }
    }
}
EOF
)
    
    # Add item to DynamoDB
    if aws dynamodb put-item \
        --table-name "$TABLE_NAME" \
        --item "$json_item" \
        --region us-east-1 > /dev/null 2>&1; then
        echo "  ✅ Successfully added cache entry for $iso_timestamp"
    else
        echo "  ❌ Failed to add cache entry for $iso_timestamp"
    fi
}

# Add cache entries for the last 6 hours
echo ""
for hour in {5..0}; do
    add_cache_entry $hour
    sleep 1
done

echo ""
echo "🔍 Verifying new cache data..."

# Query the new data
aws dynamodb query \
    --table-name "$TABLE_NAME" \
    --key-condition-expression "addr = :addr" \
    --expression-attribute-values '{":addr":{"S":"'$CORRECT_SENSOR_ADDR'"}}' \
    --region us-east-1 \
    --output table

echo ""
echo "✅ Cache data updated successfully!"
echo "📊 Cache data is now ready for:"
echo "   - Sensor ID: 5c9a3f8bb8f3cc2ff71ebd88"
echo "   - Sensor Address: $CORRECT_SENSOR_ADDR"
echo "   - 6 hourly cache entries added"
echo ""
echo "🧪 Ready to test the endpoint!"
