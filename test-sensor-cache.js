#!/usr/bin/env node

// Test script to add data to sensor cache DynamoDB table and test the endpoint
const AWS = require('aws-sdk');
const axios = require('axios');
const { MongoClient } = require('mongodb');

// Set AWS profile for facility
process.env.AWS_PROFILE = 'facility';

// Configuration
const config = {
  dynamodb: {
    region: 'us-east-1',
    tableName: 'facility-sensor-summary-dev'
  },
  mongodb: {
    url: 'mongodb://triomobil-api:<EMAIL>:27017,facility-api-dev-shard-00-01.4faai.mongodb.net:27017,facility-api-dev-shard-00-02.4faai.mongodb.net:27017/api?ssl=true&replicaSet=facility-api-dev-shard-0&authSource=admin&readPreference=secondaryPreferred'
  },
  api: {
    baseUrl: 'http://localhost:8008/facility/dev',
    authToken: '4d76ba36-f0e4-4aff-8022-8b0c97516138' // Example UUID token
  }
};

// Initialize AWS DynamoDB
const dynamodb = new AWS.DynamoDB.DocumentClient({ region: config.dynamodb.region });

async function findExistingSensors() {
  console.log('🔍 Finding existing sensors...');
  
  const client = new MongoClient(config.mongodb.url);
  try {
    await client.connect();
    const db = client.db('api');
    const sensors = await db.collection('sensor')
      .find({ 'audit.deletedAt': { $exists: false } })
      .limit(5)
      .toArray();
    
    console.log(`Found ${sensors.length} sensors:`);
    sensors.forEach(sensor => {
      console.log(`  - ID: ${sensor._id}, Addr: ${sensor.addr}, Label: ${sensor.label || 'N/A'}`);
      console.log(`    Inputs: ${JSON.stringify(sensor.inputs || {})}`);
    });
    
    return sensors;
  } catch (error) {
    console.error('Error finding sensors:', error.message);
    return [];
  } finally {
    await client.close();
  }
}

async function createTestSensor() {
  console.log('🏗️  Creating test sensor...');
  
  const client = new MongoClient(config.mongodb.url);
  try {
    await client.connect();
    const db = client.db('api');
    
    const testSensor = {
      addr: 'TEST_SENSOR_001',
      label: 'Test Sensor for Cache Testing',
      inputs: {
        analog1: { unit: 'V', min: 0, max: 10 },
        digital1: { type: 'boolean' }
      },
      customerId: '507f1f77bcf86cd799439011', // Example ObjectId
      dealerId: '507f1f77bcf86cd799439012',
      audit: {
        createdAt: new Date(),
        createdBy: 'test-script'
      }
    };
    
    const result = await db.collection('sensor').insertOne(testSensor);
    console.log(`Created test sensor with ID: ${result.insertedId}`);
    
    return { ...testSensor, _id: result.insertedId };
  } catch (error) {
    console.error('Error creating test sensor:', error.message);
    return null;
  } finally {
    await client.close();
  }
}

async function addCacheData(sensor) {
  console.log(`📝 Adding cache data for sensor ${sensor.addr}...`);
  
  // Generate test data for the last few hours
  const now = new Date();
  const cacheItems = [];
  
  // Create hourly cache entries for the last 6 hours
  for (let i = 5; i >= 0; i--) {
    const hourStart = new Date(now.getTime() - (i * 60 * 60 * 1000));
    hourStart.setMinutes(0, 0, 0); // Round to hour start
    
    const ts = hourStart.toISOString().replace(/\.\d{3}Z$/, 'Z'); // ISO format without milliseconds
    const ttl = Math.round(Date.now() / 1000) + (90 * 24 * 60 * 60); // 90 days TTL
    
    const cacheItem = {
      addr: sensor.addr,
      ts: ts,
      ttl: ttl
    };
    
    // Add analog data if sensor has analog inputs
    if (sensor.inputs && sensor.inputs.analog1) {
      cacheItem.analog1 = {
        max: { measurement: 8.5 + Math.random() * 1.5 },
        min: { measurement: 2.0 + Math.random() * 1.0 },
        avg: 5.5 + Math.random() * 2.0
      };
    }
    
    // Add digital data if sensor has digital inputs
    if (sensor.inputs && sensor.inputs.digital1) {
      const count0 = Math.floor(Math.random() * 100);
      const count1 = Math.floor(Math.random() * 50);
      
      if (count0 > 0) {
        cacheItem.digital1 = {
          "0": {
            count: count0,
            first: new Date(hourStart.getTime() + Math.random() * 1800000).toISOString(),
            last: new Date(hourStart.getTime() + 1800000 + Math.random() * 1800000).toISOString()
          }
        };
      }
      
      if (count1 > 0) {
        if (!cacheItem.digital1) cacheItem.digital1 = {};
        cacheItem.digital1["1"] = {
          count: count1,
          first: new Date(hourStart.getTime() + Math.random() * 1800000).toISOString(),
          last: new Date(hourStart.getTime() + 1800000 + Math.random() * 1800000).toISOString()
        };
      }
    }
    
    cacheItems.push(cacheItem);
  }
  
  // Insert cache items into DynamoDB
  for (const item of cacheItems) {
    try {
      await dynamodb.put({
        TableName: config.dynamodb.tableName,
        Item: item
      }).promise();
      
      console.log(`  ✅ Added cache entry for ${item.ts}`);
    } catch (error) {
      console.error(`  ❌ Failed to add cache entry for ${item.ts}:`, error.message);
    }
  }
  
  return cacheItems;
}

async function testEndpoint(sensor) {
  console.log(`🧪 Testing endpoint for sensor ${sensor._id}...`);
  
  const now = new Date();
  const since = new Date(now.getTime() - (6 * 60 * 60 * 1000)); // 6 hours ago
  const until = new Date(now.getTime() + (60 * 60 * 1000)); // 1 hour from now
  
  const params = {
    sensorId: [sensor._id.toString()],
    'ts.since': since.toISOString(),
    'ts.until': until.toISOString()
  };
  
  const url = `${config.api.baseUrl}/reports/sensor/measurements/summary`;
  
  try {
    console.log(`Making request to: ${url}`);
    console.log(`Parameters:`, params);
    
    const response = await axios.get(url, {
      params: params,
      headers: {
        'Authorization': config.api.authToken,
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log(`✅ Response Status: ${response.status}`);
    console.log(`📊 Response Data:`, JSON.stringify(response.data, null, 2));
    
    if (Array.isArray(response.data) && response.data.length > 0) {
      console.log(`🎉 Success! Found ${response.data.length} measurement summaries`);
      response.data.forEach((item, index) => {
        console.log(`  Summary ${index + 1}:`);
        console.log(`    Sensor: ${item.label || item.addr}`);
        console.log(`    Time: ${item.ts.since} to ${item.ts.until}`);
        console.log(`    Measurements: ${Object.keys(item.measurement || {}).join(', ')}`);
      });
    } else {
      console.log(`⚠️  No measurement summaries returned`);
    }
    
  } catch (error) {
    console.error(`❌ API request failed:`, error.message);
    if (error.response) {
      console.error(`Response status: ${error.response.status}`);
      console.error(`Response data:`, error.response.data);
    }
  }
}

async function main() {
  console.log('🚀 Starting sensor cache test...\n');
  
  try {
    // Step 1: Find existing sensors
    let sensors = await findExistingSensors();
    
    // Step 2: Create test sensor if none exist
    if (sensors.length === 0) {
      console.log('\nNo existing sensors found, creating test sensor...');
      const testSensor = await createTestSensor();
      if (testSensor) {
        sensors = [testSensor];
      } else {
        console.error('Failed to create test sensor');
        return;
      }
    }
    
    // Step 3: Use the first sensor for testing
    const sensor = sensors[0];
    console.log(`\n🎯 Using sensor: ${sensor.addr} (${sensor._id})`);
    
    // Step 4: Add cache data
    await addCacheData(sensor);
    
    // Step 5: Test the endpoint
    console.log('\n');
    await testEndpoint(sensor);
    
    console.log('\n✨ Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
if (require.main === module) {
  main().catch(console.error);
}
