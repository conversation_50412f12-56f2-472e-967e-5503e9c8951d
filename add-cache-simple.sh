#!/bin/bash

# Simple script to add cache data for sensor address 23BCAE
set -e

export AWS_PROFILE=facility
TABLE_NAME="facility-sensor-summary-dev"
SENSOR_ADDR="23BCAE"

echo "➕ Adding cache data for sensor address: $SENSOR_ADDR"
echo "📍 Sensor ID: 5c9a3f8bb8f3cc2ff71ebd88"

# Calculate current time and TTL
current_time=$(date -u +%s)
ttl_time=$((current_time + 90 * 24 * 60 * 60))  # 90 days TTL

# Add a few cache entries for recent hours
for hour_offset in 3 2 1; do
    timestamp_seconds=$((current_time - hour_offset * 3600))
    hour_start=$((timestamp_seconds - timestamp_seconds % 3600))
    iso_timestamp=$(date -u -r $hour_start '+%Y-%m-%dT%H:%M:%SZ')
    
    echo "📝 Adding cache entry for: $iso_timestamp"
    
    # Simple cache entry
    aws dynamodb put-item \
        --table-name "$TABLE_NAME" \
        --item '{
            "addr": {"S": "'$SENSOR_ADDR'"},
            "ts": {"S": "'$iso_timestamp'"},
            "ttl": {"N": "'$ttl_time'"},
            "analog1": {
                "M": {
                    "max": {"M": {"measurement": {"N": "9.2"}}},
                    "min": {"M": {"measurement": {"N": "2.1"}}},
                    "avg": {"N": "5.8"}
                }
            },
            "digital1": {
                "M": {
                    "0": {"M": {"count": {"N": "45"}, "first": {"S": "'$iso_timestamp'"}, "last": {"S": "'$iso_timestamp'"}}}
                }
            }
        }' \
        --region us-east-1 && echo "  ✅ Added successfully" || echo "  ❌ Failed"
    
    sleep 2
done

echo ""
echo "✅ Cache data added for sensor address: $SENSOR_ADDR"
echo "🧪 Ready to test with sensor ID: 5c9a3f8bb8f3cc2ff71ebd88"
