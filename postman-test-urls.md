# Postman Test URLs for Sensor Measurements Summary

## Configuration
- **Base URL**: `http://localhost:8008/facility/dev`
- **Authorization Header**: `4d76ba36-f0e4-4aff-8022-8b0c97516138`
- **Content-Type**: `application/json`

## Sensor IDs
- **Sensor 1**: `5c13b2bb0435d70e1010db7f` (Existing sensor)
- **Sensor 2**: `684a94ed4f70340a7b3f8658` (Test sensor with DynamoDB cache data)

## Time Range
- **Since**: `2025-06-12T01:00:00Z` (covers our cache data)
- **Until**: `2025-06-12T10:00:00Z`

## Test Cases

### 1. Single Sensor - Existing Sensor
```
GET http://localhost:8008/facility/dev/reports/sensor/measurements/summary?sensorId=5c13b2bb0435d70e1010db7f&ts.since=2025-06-12T01:00:00Z&ts.until=2025-06-12T10:00:00Z
```

### 2. Single Sensor - Test Sensor with Cache Data
```
GET http://localhost:8008/facility/dev/reports/sensor/measurements/summary?sensorId=684a94ed4f70340a7b3f8658&ts.since=2025-06-12T01:00:00Z&ts.until=2025-06-12T10:00:00Z
```

### 3. Multiple Sensors - Comma Separated (NEW FEATURE TEST)
```
GET http://localhost:8008/facility/dev/reports/sensor/measurements/summary?sensorId=5c13b2bb0435d70e1010db7f,684a94ed4f70340a7b3f8658&ts.since=2025-06-12T01:00:00Z&ts.until=2025-06-12T10:00:00Z
```

## Expected Results

### For Sensor `684a94ed4f70340a7b3f8658` (with cache data):
- Should return **6 measurement summaries** (one for each cached hour)
- Each summary should contain:
  - `_id`: `684a94ed4f70340a7b3f8658`
  - `addr`: `TEST_SENSOR_001`
  - `label`: `Test Sensor for Cache Testing`
  - `ts`: Time range for each hour
  - `measurement`: Object containing `analog1` and `digital1` data

### Sample Expected Response Structure:
```json
[
  {
    "_id": "684a94ed4f70340a7b3f8658",
    "addr": "TEST_SENSOR_001",
    "label": "Test Sensor for Cache Testing",
    "ts": {
      "since": "2025-06-12T03:00:00Z",
      "until": "2025-06-12T03:59:59Z"
    },
    "measurement": {
      "analog1": {
        "max": 9.2,
        "min": 2.5,
        "avg": 6.1
      },
      "digital1": {
        "0": {
          "count": 59,
          "firstOccurrence": "2025-06-12T03:05:32Z",
          "lastOccurrence": "2025-06-12T03:35:32Z"
        },
        "1": {
          "count": 32,
          "firstOccurrence": "2025-06-12T03:12:03Z",
          "lastOccurrence": "2025-06-12T03:42:03Z"
        }
      }
    }
  }
  // ... 5 more similar objects for other hours
]
```

## Cache Data Details

The DynamoDB cache contains data for these exact timestamps:
- `2025-06-12T03:00:00Z`
- `2025-06-12T04:00:00Z`
- `2025-06-12T05:00:00Z`
- `2025-06-12T06:00:00Z`
- `2025-06-12T07:00:00Z`
- `2025-06-12T08:00:00Z`

## What to Verify

1. **Cache Hit**: Sensor `684a94ed4f70340a7b3f8658` should return data quickly (cache hit)
2. **Multiple Sensor Support**: The comma-separated request should work and return data for both sensors
3. **Data Structure**: Response should match the swagger specification
4. **Count Verification**: The cached data is being properly counted and aggregated

## Troubleshooting

If you don't see results for the test sensor:
1. Check that the time range covers `2025-06-12T03:00:00Z` to `2025-06-12T08:00:00Z`
2. Verify the sensor ID is exactly `684a94ed4f70340a7b3f8658`
3. Check that the cache TTL hasn't expired (set to 90 days)
4. Ensure the authorization token is correct

The cache data should be returned and properly counted by the endpoint!
