#!/bin/bash

# Test script for specific sensor IDs: 5c13b2bb0435d70e1010db7f,684a94ed4f70340a7b3f8658
set -e

# Configuration
API_BASE_URL="http://localhost:8008/facility/dev"
AUTH_TOKEN="4d76ba36-f0e4-4aff-8022-8b0c97516138"

# Your specific sensor IDs
SENSOR_1="5c13b2bb0435d70e1010db7f"  # Existing sensor
SENSOR_2="684a94ed4f70340a7b3f8658"  # Our test sensor with cache data
BOTH_SENSORS="5c13b2bb0435d70e1010db7f,684a94ed4f70340a7b3f8658"

echo "🧪 Testing specific sensor IDs..."
echo "📍 Sensor 1: $SENSOR_1"
echo "📍 Sensor 2: $SENSOR_2 (with cache data)"
echo "🔗 API Base URL: $API_BASE_URL"

# Calculate time range to cover our cache data
current_time=$(date -u +%s)
since_time=$((current_time - 8 * 3600))  # 8 hours ago
until_time=$((current_time + 1 * 3600))  # 1 hour from now

since_iso=$(date -u -r $since_time '+%Y-%m-%dT%H:%M:%SZ')
until_iso=$(date -u -r $until_time '+%Y-%m-%dT%H:%M:%SZ')

echo "⏰ Time range: $since_iso to $until_iso"

# Function to make API request
test_sensors() {
    local sensor_ids="$1"
    local test_name="$2"
    
    echo ""
    echo "🧪 $test_name"
    echo "📍 Sensor ID(s): $sensor_ids"
    
    # Build URL
    local url="${API_BASE_URL}/reports/sensor/measurements/summary?sensorId=${sensor_ids}&ts.since=${since_iso}&ts.until=${until_iso}"
    
    echo "🌐 URL: $url"
    echo ""
    
    # Make request with timeout
    echo "📡 Making request..."
    local response=$(timeout 30 curl -s -w "\nHTTP_STATUS:%{http_code}" \
        -H "Authorization: $AUTH_TOKEN" \
        -H "Content-Type: application/json" \
        "$url" 2>/dev/null || echo "REQUEST_FAILED")
    
    if [[ "$response" == "REQUEST_FAILED" ]]; then
        echo "❌ Request failed or timed out"
        return 1
    fi
    
    # Extract status and body
    local http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    local response_body=$(echo "$response" | sed '/HTTP_STATUS:/,$d')
    
    echo "📊 HTTP Status: $http_status"
    
    if [ "$http_status" = "200" ]; then
        echo "✅ Success!"
        
        # Try to parse JSON and count results
        if command -v jq >/dev/null 2>&1; then
            local count=$(echo "$response_body" | jq 'length' 2>/dev/null || echo "unknown")
            echo "📈 Number of results: $count"
            
            if [ "$count" != "null" ] && [ "$count" != "0" ] && [ "$count" != "unknown" ]; then
                echo "🎉 Found measurement summaries!"
                
                # Show summary of each result
                echo "$response_body" | jq -r '.[] | "  - Sensor: \(.label // .addr), Time: \(.ts.since) to \(.ts.until), Measurements: \(.measurement | keys | join(", "))"' 2>/dev/null || true
                
                if [[ "$sensor_ids" == *"$SENSOR_2"* ]]; then
                    echo "✨ This includes our test sensor with cached DynamoDB data!"
                fi
            else
                echo "⚠️  No measurement summaries returned"
            fi
        else
            echo "📄 Response (first 500 chars):"
            echo "$response_body" | head -c 500
            if [ ${#response_body} -gt 500 ]; then
                echo "... (truncated)"
            fi
        fi
    else
        echo "❌ Request failed with status: $http_status"
        echo "📄 Response: $response_body"
    fi
}

echo ""
echo "🚀 Starting tests..."

# Test 1: First sensor only
test_sensors "$SENSOR_1" "Test 1: Sensor $SENSOR_1 only"

# Test 2: Second sensor only (our test sensor with cache)
test_sensors "$SENSOR_2" "Test 2: Sensor $SENSOR_2 only (with cache data)"

# Test 3: Both sensors together (testing comma-separated feature)
test_sensors "$BOTH_SENSORS" "Test 3: Both sensors together (comma-separated)"

echo ""
echo "📋 Summary:"
echo "  - Sensor 1 ($SENSOR_1): Existing sensor"
echo "  - Sensor 2 ($SENSOR_2): Test sensor with DynamoDB cache data"
echo "  - Cache data exists for: 2025-06-12T03:00:00Z to 2025-06-12T08:00:00Z"
echo "  - Testing both single and multiple sensor requests"

echo ""
echo "🔗 Postman URLs for manual testing:"
echo ""
echo "Single sensor 1:"
echo "${API_BASE_URL}/reports/sensor/measurements/summary?sensorId=${SENSOR_1}&ts.since=${since_iso}&ts.until=${until_iso}"
echo ""
echo "Single sensor 2 (with cache):"
echo "${API_BASE_URL}/reports/sensor/measurements/summary?sensorId=${SENSOR_2}&ts.since=${since_iso}&ts.until=${until_iso}"
echo ""
echo "Both sensors (comma-separated):"
echo "${API_BASE_URL}/reports/sensor/measurements/summary?sensorId=${BOTH_SENSORS}&ts.since=${since_iso}&ts.until=${until_iso}"
echo ""
echo "Headers:"
echo "Authorization: $AUTH_TOKEN"
echo "Content-Type: application/json"

echo ""
echo "✨ Test completed!"
