# CRITICAL
- facility/fleet redis instance type'larını gözden geçir. m5 yerine m7-c7 gibi kullanmak daha iyi olabilir.
- facility/fleet-tcp ve facility/fleet-api EBS metrikleri için grafikler ve alarmlar kur.
- facility-forklift-safety-summary ve facility-forklift-safety-summary-dev tablolarını `deviceIdentifier` yerine `device-identifier` hash key'i ile kullan
- check if fleet-vehicle-address-cache table is being used efficiently
- alarm emailleri/kayıtları acknowledge özelliği (alarm rule > action > reminder > recovery can be more detailed with more states: ie. acknowledged, resolved, escelated)
- alarmlara not girme <PERSON>ği
- [ERROR] alarmRule hook error TypeError: Cannot read property 'toString' of undefined
- fleet-logs bucket'ı altındaki cloudfront loglarını ya disable et ve sil ya da 90 günle sonra otomatik silinsin (facility için de geçerli)
- facility dev ve v1 ortamları için, http-secuirty-headers lambda fonksiyonu yerine fleetus'taki gibi Response headers policy kullan.
- AWS Backup kullanarak DynamoDB'leri backup'a bağla
- websocket için gelen requestler farklı bir şekilde/yerde işlenmeli
- token/yetki hatası nedeniyle uygulamanın ürettiği 403 mesajları ile WAF rate-limit vs nedeni ile oluşan 403 hataları ayrıştırılabiliyor olmalı
- https://aws.amazon.com/premiumsupport/knowledge-center/elastic-beanstalk-host-attacks/
- 5XX & 4XX hataları monitör edilmeli. çok fazla hata yapanlar bloklanmalı.
- customer ve dealer update/delete yapılırken yapılan config.models kontrolü alarmRule ve dashboard'da eksik...
- redisutils => delete alarm rules? when createdby user is already deleted (redis kayıtları da temizlenebilir, veya zaten otomatik temizlenebilir bir sonraki run'da)
- tüm modellerde "keys: label: 1, customerId: 1, 'audit.deletedAt': 1" tipi index'ler için "sparse: true" yerine "partialFilterExpression: label: $exists: true" (deploy'dan önce mevcut index'ler manuel drop edilmesi lazım production DB'de)
- stationary (tracker) ve offline (all types) history/summary rapor endpoint'lerini oluştur
- anchor'lar ve gateway'ler için latest raporu olmalı, veri göndermeyenleri vs listeleyebilmeliyiz
- anchor editlendikten sonra adres değiştiyse, eski anchor'u silmek gerek. yenisi ekleniyor ama eskisi de kalıyor.
- setParent hook'unu bi gozden gecir, kullanıcı eklerken/duzenlerken birden fazla müşteri/bayi vs oldugunda (ayrı modellerde ayrı müşterier falan) beklendiğı gibi array olarak ekleniyor mu, kontrol edilmeli.
- kullanıcı silinince ilgili alarm kuralları ve redis'teki kalıntıları da silinmeli mi? eğer müşteride başka kullanıcı kalmadıysa yapmalı
- kullanıcının yetkileri değişirse, alarm kuralları da taranıp, görmeye yetkisi olmayan cihazlar alarm kurallarından temizlenebilir.
- anchor latest status'u List Anchor endpoint'ine eklemek lazım, Gateway modülünde olduğu gibi
- sensor özet raporunda analogInputFormulated kısmı eski tip formüle göre çalışıyor. ya yeni tipi desteklemesi gerek (kısmi de olsa, eski usüle yakın formüller için belki) ya da en azından hata vermeyecek hale gelmeli
- handle async/await errors globally using with unhandledRejection / UnhandledPromiseRejection events etc.
- şu kısımlara gerek var mı? default hook'lar belirleyemez miyiz? veya en azından sadece 'read' vs yazalım bu koca şeyi değil hooks: ( @hooks { req, res, model: model }, ['read']
- latest position raporu x-trio-total-count tutmayabiliyor. (latest position'ı olmayan cihazlar sebebiyle olabilir)
- make sure summary report caches works with pagination as well. it's probably not a problem since the endpoints allow up to 20 devices so it is probably fitting into the _page=1 but if _perPage is requested with less than 20 and _page=2+ cache result probably would not work. (şimdi baktım da cache, cihaz bazlı tutuluyormuş. buyuk ihtimalle sorun olmaz)
- bölge/alan silinirse/düzenlenirse nasıl çalışıyor. redis'te alarm:rule & alarm:watch kontrol edilmeli
- insights kullanarak tüm lambda, API ve TCP loglarını error, null, undefined, NaN vs ara
- https://github.com/awsdocs/elastic-beanstalk-samples/tree/master/configuration-files
- maintanance scriptini ayrı bir uygulamaya geçirmek düşünülebilir (aynı API server çalışabilir ama worker gibi sadece maintanance scripti çalıştıran, request kabul etmeyen) veya en azından bir worker thread içerisinde çalıştırılabilir mi?
- redis to Valkey migration https://hn.algolia.com/?q=valkey

## MAJOR
- (v2) .location.geo.lat/lon isimlendirmesinde geo yerine gps kullanılabilir
- (v2) add type attribute to anchor model
- (v2) alarm geçmişinden x-trio-total-count'u kaldır, hatta tüm summary raporlarından veya en azindan bazılarından da kalkabilir. bu tabi, toplam alarm sayısı aldığımız durumlar için sıkıntı teşkil edebilir. aşağıdaki maddeyle beraber düşünülmeli
- (v2) alarm geçmişinde tarih kullanımı zorunlu olabilir, violation veya resovery ts'leri beraberce veya en azından birisi olmak üzere kullanılabilir. default ve max date range'ler belirlenebilir. genel bir ts.since/until parametresi eklenebilir. Örnek hata mesajı "At least one of violation.ts and recovery.ts query-string parameters are required"
- (v2) dynamodb bazlı history raporlarında _limit yerine pagination kullanilabilir belki ama tabi zor olabilir LastEvaluatedKey-ExclusiveStartKey kullanılması lazım. bunun için LastEvaluatedKey'ler redis'te tutulması falan gerekebilir. (tabi redis'te de sonsuza kadar tutamayiz. redisten silinen bir LastEvaluatedKey icin page=2 requesti gelirse error code dönebilir, adam pagination'ı adam gibi ilk sayfadan yapsın gelsin) bazı raporlarda, post-filtering yapıliyor (örnek: sensor/count/history raporunda formula.thorttle olan cihazlarda)
- (v2) position kelimelerini location'la değiştir. (MAYBE)
- Latest position report'ta floorPlanId,x,y attr'leri location.indoor altına al. hatta location.geo da eklenebilir.
- (v2) tüm outputtaki ts'leri preference timezone'da iso formatla (türkiye saati mesela Z yerine +03:00'le suffix'lenmeli)
- cihazlara lat/lon değerleri de kaydedilebilmeli
- (v2) fingerprint ve anchor modellerinde floorPlanId ve x, y koordinatları location.indoor attribute'u altına alınabilir. Bunun hem facility zone gösterme/raporlarda filtrelerde hem de alarm-kontrolü tarafına etkisi olacaktır
- (v2) zone raporlarındaki duration.outside.max -> duration.outside.tolerance VEYA daha sade olarak duration.inside.min -> duration ve duration.outside.max -> tolerance olarak değişebilir
- (v2) customer ve dealer modellerindeki addressList, phoneList, emailList -> address, phone, email olarak değiştirilebilir
- (v2) rename sensorCounter to counter or counterSensor (maybe?)
- (v2) add default value ASC to _sortOrder trait
- (v2) Zone Presence Summary Report response attr duration.sum -> duration.total olarak değiştir
- (v2) permissionlara "except" alanı eklenebilir. mesela, müşteri altındaki tüm cihazları görsün ama sadece 1-2 cihazı görmesin. bayi altındaki tüm cihazları görsün ama 1 müşterinin cihazları hariç gibi.
- (v2) temperature latest ve history raporlarında measurement'ı kaldır, direk temperature key olsun
- (v2) tracker ikon tanımlamaları default, warning, error gibi olmasın. direk default kullanılabilir (default.cson'daki default tanımlamaları da elden geçmesi gerekir) Benzer durum zone color tanımlamasında da var; orada da tek renge indirgenmeli.
- (v2) get websocket url yerine get mqtt url gibi birşey haline
- (v2) Özet raporlarında min/max değerlerini verirken bir de bu min/max değerlerinin hangi timstamp'te olduğu bilgisine de yer verilebilir. {<key>: max: <value>, min: <value>} yerine {<key>: max: {ts: <ts>, value: <value>}, min: ts: <ts>, value: <value>} gibi
- /reports/energy/measurements/history _pull query-string'i desteği olsun. 19 tane parametre taşınıyor, geliyor
- (v2) consider using plurals for device type in alarm rule since they are arrays (trackers, sensors, temperatureSensors, energyMeters)
- (v2) latest positions ve positions history raporlarına _pull=zone ile beraber olmak üzere, zone:array(object{_id,label}) eklenebilir
- (v2) id-label ve id-addr-label modellerini ilgili yerlerde kullan, duplicate tanımlamalar olmasın
- (v2) _sortby field'lar gözden geçirilebilir. mesela özet raporlarında _sortby=label yerine _sortby=tracker.label şeklinde ifade etmek daha doğru olabilir
- (v2) outage raporlarında duration.min/max yerine outage.duration.min/max denebilir
- (v2) latest endpointlerindeki snr ve isBatteryLevel gibi attribute'lar status attribute'u altına alınabilir
- (v2) alarm history'de (varsa diğer ilgili yerlerde de) offline yerine isOffline kullanılabilir
- (v2) temperature latest ve history raporlarında ölçüm değerini mesasurement.temperature altında tutmak yerine mesasurement olmadan sadece temperature kullanabiliriz, (çift temperature sensor olan cihazlarda nasıl olurdu düşünmeli)
- (v2) indoor/positions/latest raporunda floorPlanId ve zoneId yerine floorPlan ve zone altında _id, label detayına yer verilebilir
- (v2) tracker.marker'da default, warning, alert prop'larını kaldır ve sadece marker: {icon, color} kalsın
- (v2) revisions'ı jsondiffpatch bazlı değil, önceki kayıdı tümüyle kayıt altına alacak şekilde düzenlemeli.
- (v2) indoor "position" yerine, "location" kelimesi kullanılabilir. rapor isimleri ve path'leri değişebilir
- (v2) firstOccurrence/lastOccurrence yerine first/last kullanılabilir
- (v2) validHours geçen yerlerde 00:00, 23:59 şeklinde kullanımı aynı zamanda 00:00:00, 23:59:59 olarak çalışacak şekle getirmeli. Ayrıca regex pattern olarak ^\d{2}:\d{2}:\d{2}$ kullanılmalı (see driving summary report)
- (v2) latest endpoint'lerinden pagination'ı kaldır ve Id ile sorgulamayı zorunlu kıl. bu haliyle, latest değeri olmayan cihazlar pagination'ı manasız hale getiriyor
- (v2) zone modelinde location alanı position olarak adlandırılabilir.
- (v2) make either `vehicleId` or `temperatureSensorId` query-string parameters as required for latest temperature measurement report (check report.js)
- (v2) Indoor Events Summary Report response body'deki event, status olarak isimlendirilebilir (veya _groupBy=status, _groupBy=event olarak isimlendirilebilir)
- (v2) `/reports/zone/...` endpointleri `/reports/indoor/zone/...` olarak değiştirilebilir. (config.reports.maxInterval.short.months ayarını unutma)
- (v2) token'ları header'da Authoroization ile değil, X-TRIO-TOKEN diye bir header'la göndermek daha iyi olabilir
- (v2) `/reports/indoor/positions/latest` endpointi response'da `zoneId` array dönmeli
- (v2) Indoor (Array) Coordinates maxItems should be specified
- (v2) driving/summary _groupBy=travel yerine _groupBy=trips olabilir
- (v2) latest raporlarında _sortBy=label özelliği, _sortBy=xxx.label gibi düzenlenebilir. vehicle.label, forklift.label gibi
- (v2) `/vehicles/{id}/commands` da `mileage:` yerine `setMileage:` kullanılabilir
- (v2) özet endpoint'lerinde duration attibute'u altında toplam süreler bazen `sum` ile bazen de `total` ile ifade edilmiş. aynı olsa daha iyi olacaktır. (`total` daha uygun gibi)
- (v2) alarmRule.schedule.date.startDate/endDate => ...date.start(At)/end(At) olarak değiştirilebilir
- (v2) `reports/alarm/history` cevapları arasında vehicle fuel ile ilgili `reference` diye bir kullanımına bir alternatif düşünülebilir
- (v2) /reports/trueai/object/count/latest-history-summary endpoint'leri kaldırılacak (ilgili redis kayıtları, dynamodb tabloları, config'ler temizlenmeli)
- (v2) /reports/trueai/event/... endpointleri /reports/trueai/events/... olarak değiştirilmeli.
- (v2) latest raporlarında deviceId query-string parametresi max array length'in _perPage'den fazla olması durumları kaldırılabilir
- (v2) vehicle.profile enum değerleri UPPER_CASE olmalı
- (v2) /reports/trueai/event/... endpointlerinde PEDESTRIAN, TRUCK, FORKLIFT type'ları PEDESTRIAN_IN_ZONE, TRUCK_IN_ZONE, FORKLIFT_IN_ZONE olarak değiştirilebilir. (depreciate edilen type'lara ...IN_ZONE eklenerek dönebiliriz.
- (v2) zone summary percentage'ı query'den çıkar, response'da hesaplayıp yazabiliriz. bir de tabi, validHours ve groupBy'ı dikkate almalı. outage/summary'ye bakabiliriz

- _vehicleReport dosyasındaki fonksiyonlara `req` parametresini geçmeye gerek var mı? `@req` zaten fonksiyonların içinde var olabilir.
- `_.sortBy` kullanılan ve ardından `_sortOrder` query-string parametresine bağlı olarak `Array.reverse()` yapıldığı durumlar `_.orderBy` fonksiyonu ile değiştirilebilir. Örnek: `@res.json err, _.orderBy results[...@req.params._limit], 'ts', @req.params._sortOrder.toLowerCase()` (yer yerde .toLowerCase() kullanmak yerine tek bir yerde bu parametreler lower case yapılabilir)
- Raporlardaki mesai saatleri filtreleme kısmında gün bazlı yapılan filtrelemeye gün bazlı saat aralığı girilmesi
- G sensörüne sahip cihazların kontak kapalı durumda hareket etmesi halinde alarm oluşması ve müşteriye bilgilendirilmesi (motorsiklet çalınması, aracın çekiciye yüklenmesi gibi durumlar için)
- Dahili bataryalı kablolu cihazların bağlantısı sökülmesi halinde alarm oluşması ve müşteriye bilgilendirilmesi
- try/consider using `mongodb+srv://` connection string. should be supported with MongoDB Driver v3+ which is used? by mongolayers (update: mongolayers3 does not support this yet)
- subscription vehicle harici modellerde de kullanıldığı durumda vehicle modelinde update ve delete için yazılan `isCommitmentMet` kontrolü hook'a taşınmalı.
- https://us-east-1.console.aws.amazon.com/cloudfront/v3/home#/savings-bundle/overview
- revisions tablosu jumbo DB'ye taşınabilir
- forklift-yaya yakınlaşması yüksek hızda gerçekleşiyorsa tehlikeli olarak ayrıştırılmadı.
- `.util/geolocation` endpoint being open to public does not make sense. index files probably needs to have response header (maybe via edge functions) to return geolocation info
- subscription cancelled'a çekildiğinde (subscription DELETE işlemi ile), subscription'a ait diğer verileri temizlemek gerekebilir. en azından diğer değerleri göstermemeli
- trueaiDevice attrs.isVideoUploadEnabled için boolean değere ek olarak expiry date özelliği de ekle
- add functionality to the gulp task to sort swagger by `parameter`, `paths`, `defintion` keys
- Özellike cevaplarda yapılan _.omitBy/_.isNil kontrolü yerine deepCompact fonksiyonu kullanılabilir.
- facility swagger'dan streamax'la ilgili şeyleri kaldırabiliriz.
- `/driving/behavior/summary` endpoint is missing
- mesai saatleri için gün gün ayrı saatler belirlenebilmesi gerekli
- vehicle-get ve streamax-get beraber kullanıldığında allowAdditionalProperties
- base report'larda (_vehicleReport, _forkliftReport etc.) fonksionlara req, res gibi parametrelerini geçmeye gerek yok. @req, @res doğrudan kullanılabiliyor zaten. (örnek fonksiyon adı: retrieveSummary, getLocation)
- generate a subset of swagger files to share with customers which does not include certain endpoints like customer, dealer etc. and without PUT/DELETE/POST for device endpoints like vehicle, temperatureSensor etc.
- toUTCZ kullanılmayan yerler var. projede ara: `.toISOString().split`
- consider using https://www.npmjs.com/package/setheaders instead of https://www.npmjs.com/search?q=keywords:setHeader
- araç-bölge raporlarına bölge içi km, idle vs bilgiler eklenebilir.
- change AWS certificate renewal from email to DNS: https://us-east-1.console.aws.amazon.com/acm/home?region=us-east-1#/certificates/7f1682d5-7565-432a-b07d-198b34d40e5a
- maintenance scriptini ayrı app'e taşı. cron yerine requestler arasında 15dk vs bırakarak çalıştır, db-sync'teki gibi.
- redis prefix `command:pending` yerine `command:pending:` olmalı. (tcp, engine, api hep beraber)
- ayrı bir performans hook'unda mongodb sorgularında imei exact match gibi max 1 sonuç çıkabilecek query'lerde hooklar tarafından eklenen sort/count vs gibi şeyler iptal edilebilir, `_perPage` otomatik olarak 1'e vs çekilebilir, x-total-count ise cevap bulunursa manuel olarak 1 olarak set edilebilir (ilgili: komutadmin query'leri)
- latest endpoint'lerinde x-trio-total-count, cihaz sayısına göre değil dönen satır sayısına göre olmalı.
- Configure a lifecycle policy to limit the number of application versions to retain for future deployments. https://docs.aws.amazon.com/elasticbeanstalk/latest/dg/applications-lifecycle.html
- redis.reports.cache kullanılan yerlerde büyük JSON'lar kaydediliyorsa öncesinde lz-string'den geçirilebilir
- consider adding query-string params for forklift/safety/history: distance.lessThan, interaction.anchor=boolean, interaction.tracker=boolean
- low battery için alarm opsiyonu ekleyebiliriz.
- add response headers for summary reports where response is collected from cache. (x-trio-cached-result = true/partial gibi)
- response JSON bir fonksiyondan geçirilip, `ts`, `tracker` vs gibi cihaz identifier'lar üst tarafa alınabilir.(https://www.npmjs.com/package/sort-keys)
- status 404 için ayrı bir WAF kuralı olabilir botları falan engellemek için. daha sıkı bir kontrol ile IP bloklanabilir.
- https://docs.aws.amazon.com/AmazonCloudFront/latest/DeveloperGuide/origin-shield.html
- response body object key order can be identical to swagger definition order of keys
- fleet/facility VPC proxy (prod) kullanılmadığında kapalı tutulmalı
- https://aws.amazon.com/network-firewall/?whats-new-cards.sort-by=item.additionalFields.postDateTime&whats-new-cards.sort-order=desc
- floorplan ve logo upload edildiğinde, imajı s3'e yazmadan önce kullanıcının floorPlan/customer/dealer'ı güncelleme yetkisi var mı kontrol edilebilir
- müşteri ve ayrıca cihaz bazlı makul bir max alarm kuralı sayısı olabilir
- müşteri bazlı makul bir max kullanıcı sayısı olabilir.
- list register map endpoint'ine `register.addr` ile arama imkanı sağlanabilir.
- alarmlardaki email ve mobil numara kayda geçmeden önce, verify edilebilir.
- temperature _sortOrder & _sortBy eksik
- _page ve _perPage parametreleri kullanılırken x-trio-total-count dışında kalan bir sayfa isteniyorsa 400 koduyla birlikte hata mesajı yazılmalı
- https://aws.amazon.com/lookout-for-metrics/
- https://aws.amazon.com/about-aws/whats-new/2021/02/introducing-amazon-cloudfront-security-savings-bundle/
- https://docs.aws.amazon.com/compute-optimizer/latest/ug/getting-started.html
- https://github.com/aws-samples/aws-cloudfront-samples/tree/master/update_security_groups_lambda
- extend modülü kullanmak yerine Object.assign kullanabiliriz mi?
- https://docs.aws.amazon.com/elasticloadbalancing/latest/application/listener-authenticate-users.html
- A4 protokolü ile gelen temperature2 değerleri de raporlanabilmeli
- customer-update/delete ve dealer-delete işlemlerinde config.models üzerinden yapılan kontrollerde alarmRule ve dashboard modelleri kontrol edilmiyor. bu modellerde customerId ve dealerId attr'leri array olduğu unutulmadan kontrole eklenmeli
- consider using https://www.npmjs.com/package/dynamoose
- sensor raporlarının uzun süre cache'lenmesi, analog değerler için formül değişmesi durumunda sorun oluyor.
- latest data'sı header'da bulunan total count'tan daha az sayıda dönmekte. sayfalar'da perPage=20 iken response'larda 20'den daha az kayıt dönmekte. (redis'te latest değeri olmayan cihazlar için geçerli durum)
- zone report summary percentage calculation might be inaccurate. should consider, valid hours as well.
- bölge raporu query'lerindeki "ORDER BY  ts  ROWS  BETWEEN 1 FOLLOWING AND 1 FOLLOWING" kısmını araştır. LEAD ile beraber kullanıldığında doğru LEAD satırı işaret ediyor mu?
- add maps.google.com CSR for facility-admin
- WebSocket APIs in Amazon API Gateway: https://docs.aws.amazon.com/apigateway/latest/developerguide/apigateway-websocket-api.html
- password complexity kuralı gerekli (müşteri bazlı ayar olabilir)
- 422 status code should return response body as "{"error":{"code":422,"message":"Unprocessable Entity"}}"
- alarm history endpoint'i aggragate kullanarak revize edilebilir, toplam işlemin hız kazanması açısından
- redis.hgetall yapıldıktan sonra bi değişıklik içın tek tek redis.hset yapılıyorsa bunları hmsetall ile değiştirmek daha iyi olabilir. (redis.hdel de kullanılıyorsa hdel yeterli olur, hmdel komutu yok, hdel hmdel gibi çalışabiliyor) tracker modülü update fonksiyonunda benzer birşey yapıldı
- data export fonksiyonalitesi lazım. export alırken belli periyotlarla (saat başı vs gibi) sample alma seçeneği olmalı.
- opt-out'a seçenek olarak, sadece hatırlatmaları kapa ve ayrıca belki sadece bu cihazı alarm kuralından çıkar diye bir seçenek olabilir
- min,max ve avg için, gruplama özellikle sıcaklık özet raporu ednpointi hazırlanabilir (frontend için)
- Değer değişimi raporu gibi bir rapor formatı olabilir. yakalamak istenen değişim miktarları (yüzdesel de olabilir, sayısal da olabilir) yakalanıp raporlanabilir. (Atık boşaltma raporu için gerekli)
- add tagging capability to models
- redshift'i aylık tablolara böl. query'ler union all'la yapılır. düzenli olarak yeni tablo eklenip eskileri drop edilir. bunun içın firehose'un destination'ı da aylık olarak değiştirip yeni tabloya çevirmek lazım
- energy summary report uses Max/Min instead of Last_value/First_value (for energy type attributes) this could be causing problems with caching. it should go back to Last_value/First_value anyway once the devices are fixed and sends energy measurements in correct order
- filter people counter history and eliminate one-off odd records from the result set
- can add avg to sensor & energy summary reports
- while getting thing ids to run for alarm history report in `_alarmReport`, instead of just `config.reports.alarm.history.searchLimit` (set to 500 initially) find/filter things that are associated with an alarmRule, so that it will be less likely to skip a thing if the user has more than 500 things. and this should speed up alarm history queries anyway.
- alarm rule label, customer ve createdBy ile unique indekslenebilir
- alarm mqtt could be based on alarm history report permission
- implement updates with dot notation so that when updating nested objects with optional fields, it would not overwrite the whole object. probably convert $set data to dot-notiation competible version prior to update operation. https://docs.mongodb.com/manual/core/document/#dot-notation
- implement to heal/remove all the things (attributes, alarm rules, alarm waits etc.) that are store in redis (not latest datas)
- restrict req.body globally from GET requests
- redshift summary raporlarında unbounded preceding AND unbounded following ibaresi yerine daha kısa/yeterli bir frame clause kullanınlabilir
- if possible cache summary reports when _groupBy is enabled as well
- summary raporlarında _perPage limit 50. bunu 20'ye çekip, cihazId'si gönderilmediğinde, pagionation'la siğer cihazlar da çekiliyor olmalı. (yukardaki cache konusunu unutma)
- indoor/positions/summary sortBy=label çalışmıyor.
- redis repair is finished sürelerini kontrol et. 60 dk'da bir çalışacak şekilde ayarlanmıştı.
- https://docs.aws.amazon.com/glue/latest/dg/partition-indexes.html
- add unique index key to reports model, path & name
- redis-promisify yerine redis kullanılabilir. promise desteği var versiyon 4.0.0 itibari ile
- res.json fonksiyonunda result'ı dönmeden önce deepCompact fonksiyonundan geçirilebilir. böylece her yerde deepCompact kullanmak zorunda olmaz
- instead of using `client_max_body_size 5M;` in `.platform/nginx/conf.d/upload_limit.conf` which is a global setting, make it applicaple for record file upload endpoint only. something like `location ~ /records/[a-f0-9]+/files$ { client_max_body_size 5M; }` (this, as it is, cannot be deployed due to an error)
- _.pickBy kullanımlarını gözden geçir. _.isNil ile kullanıyor olmak daha iyi olabilir. sadece _.pickBy kullanınca false value'lar da siliniyor. yerine _.omitBy(object, _.isNil) kullanılabilir
- alarm rule reminder min 30dk olmalı. (frontende de gerekli düzenleme yapılsın. default olarak da 60dk diyelim)
- bazı raporlarda req.params._pull ile snr çekiliyor. ama snr -20 ve 10 arasında değilse 422 hatası oluyor. snr -20 ve 10 arasında değilse snr dönmemeliyiz
- cihaz silinirse, alarm kurallarından da temizlenmeli. eğer alarm kuralında o silinen tek cihaz vardı ise, alarm kuralı da silinmeli.
- `trueai/events/summary` endpointine ihtiyaç var. count hesaplarken v2'deki identifier uniqness'ı dikkate al.
- şifreler kurala tabi olmalı https://www.npmjs.com/search?ranking=popularity&q=password%20strength
- swagger'dan `"properties": {}` kısımlarını temizle
- mongodb.api event `reconnect` neden çok oluyor (mu)?
- alarm:rule'ları redis'e eklerken expiration da belirtilebilir, 30 gün vs. maintanence scripti de üstüne yazar.
- SMS alarmı kurulabilecek ülke listesi belli olmalı.
- sistem notificaiton'ları mekanizması eklenmeli. kullanıcı bazlı/müşteri bazlı/tüm kullanıcılar bazlı

## MINOR / LATER
- cihaz'ların addr'leri değişirse, alarm kuralı (rule/watch) redis değerleri güncellemesi gerekiyor olabilir
- (label) sort işi TR karakter sensitive çalışmıyor olmayabilir
- anchor'lar ve gateway'ler için latest raporu olmalı, veri göndermeyenleri vs listeleyebilmeliyiz (bu modellerde latestSignalAt yok muydu?)
- floorplan silinince ilgili zone'lar da silinmeli? ya da en azından başlangıç olarak, bölge giriş-çıkış raporlarında silinmiş kat planına ait bölgeler hesaba katılmamalı
- https://opensource.zalando.com/restful-api-guidelines/
- https://docs.aws.amazon.com/athena/latest/ug/engine-versions-reference.html
- facility tarafındaki raporlarda mesai saati filtresi
- kullanıcı adı'nda türkçe karakter veya upperCase karakter olan kullanıcıların kullanıcı adlarını değiştirmesi için uyarı göstermeli
- user SSO enable edildiğinde password, salt, algorithm vs alanları temizle
- permission'ları dönmeden önce, silinmiş olanları temizle. (dealer siliniyor, customer ve diğer _id bazlı kayıtlar da temizlenebilir)
- araç/forklift için IMEI exact search yerine includes şeklinde arama yapmaya izin verilebilir
- vehicle locations latest endpoint'ine idle.since, ignition.on/off.since gibi parametreler eklenmeli.
- https://aws.amazon.com/apprunner/
- https://aws.amazon.com/waf/features/bot-control/
- consider using p-each-series instead of p-iteration/forEachSeries
- consider using https://www.npmjs.com/package/dynamodb-toolbox or https://electrodb.dev instead of Dynogels
- alarm rule (tracker) position objesi altında zone ve stationary minProperty:1, maxProperty:1 şeklinde kullanılması yerine oneOf altına alınabilir.
- maybe use rolling window rate limiter for certain endpoints. summary raporları vs request sayıları token ve IP bazlı kısıtlanabilmeli. bu epey bir ölçüde WAFv2 sayesinde yapılıyor. Belki daha detaylı bir limit koyulabilir application tarafında.
- auto-bump bin/package.json with each deploy
- revizyon'larda eski/yeni label/name/adres ile arama yapma özelliği olmalı
- revizyon'lara müşteri ve bayi verileri de eklenebilir, aramaya tabi edilebilir.
- add new alarm rule action recipient types for TCP and MQTT connections
- limit max number of alarm rules per user
- rapor çekerken, rapor tarih aralığı, asset'in createdAt tarihinden önce olmamalı. bi hook ile filtrelenebilir
- musteri okuma hakkı olmayip, kullanici yaratma izni olan portal kullanicilari, bi kullanici yarattiktan sonra yarattigi kullanici uzerinde yetki sahibi olabilmeli? bunu saglamak icin de permissionlarına ilgili kullanicinin yetkisi push edilmeli, _id bazlı
- freeze mongodb version in celalo/mongolayer package.json
- check 'Disallow Additional Properties' of all objects in the swagger file. and automate checking this by script so it's never forgotten.
- trim (all) input fields (via POST/PUT/PATCH) from leading and trailing empty chars like license plate and imei
- handle integer based searches with moreThan, lessThan etc support
- hook.search should support using $regex if search string includes % probably as in MYSQL LIKE search. %search_string should be searched with regex ending with $. search_string% should be searched with regex starting with ^. %search_string% should be search without ^$
- if user permissions is updated, websocket redis sessions might need to updated at userMods-afterUpdate hook as well. because some vehicles might be removed from a user's read authenticaion
- property set up maxmemory-policy of elasticache redis instances for instance needs. i.e. maxmemory-policy might need to be set as allkeys-lru
- JSON output haricinde CSV ve Excel output da verebilmeli endpoint'ler... hatta bu durumlar başka perPage ve limitlere tabi olabilir... 
- https://stackoverflow.com/questions/33196237/how-to-set-expire-when-using-redis-geoadd
- can make use of 405 method not allowed status in addition to 403 status
- maybe (probably no need to thanks to AWS WAF) cache error responses (4XX, 5XX) for PUT/PATCH/POST/DELETE requests. Cloudfront does not cache these but if for example 400 or 405 happens, it can be cached based on body! so that it will not reach to API server when the same request is done several times over and over again. On the other hand AWS WAF would block once 4XX, 5XX requests are high.
- (node:95195) DeprecationWarning: collection.update is deprecated. Use updateOne, updateMany, or bulkWrite instead.
- Issue with array of strings validation https://github.com/apigee-127/swagger-tools/issues/380
- maybe don't retrieve addr attribute from dynamodb for history reports if possible as it is deleted before results are returned
- revisions'a POST action'ları konmayabilir, boşa yer kaplamasın
- token oluşturulduktan sonra herhangi bir session token'la çakışmadığından emin olmalı. session:token:- kontrol edilip, eşi yoksa dönmeli.
- redisUtils repair yapılırken silinmiş user'ların session'ları da silinebilir
- https://github.com/kogosoftwarellc/open-api
- don't accept ts.since earlier than 30 days for diagnostic endpoints
- alarm/history request limiti çok düşük, ya 10'dan fazla cihaz seçmeye izin ver... ya da limiti artır, performansı gözeterek
- alarm kuralına webhook eklerken https:// olması aws waf filtresine takılıyor
- dealer bazlı yetkisi olan kullanıcılar için belirli periyotlarda şifre değiştirilmesi zorunlu olmalı. /auth endpointi token dönerken bir response header'la bilgi iletilebilir. frontend/admin panelde bu bilgiye dayanarak prompt çıkabilir.
- check alarm rule optout token signature with third param: false
- kullanıcı adlarına `-_.` koyabilmeliyiz
- alarm history query'lerinde herhangi ts ile sorgu yapılmazsa tarama yapılan süre çok mu geniş oluyor? time range sorguları max 1 veya 6 ay gibi gibi bi range'de yapılabilmeli. eğer tarih girilmediyse de otomatik bi range varsayılabilir.
- şifreler 3 ayda vs expire olabilmeli (müşteri bazlı ayar olabilir)
- maybe use pagination with with the Bucket Pattern https://www.mongodb.com/blog/post/paging-with-the-bucket-pattern--part-1

### COSMETIC / FUTURE / MAYBE
- add last updated status to tables so it does not need to be checked at the client side if all customers were previosuly retrieved and cached. Alternatively querying API with createdAt and updatedAt can be used
- https://github.com/dominictarr/JSONStream can be used to decrease memory usage, especially for large responses
- can improve start up speed by not attempting to create indexes each time. indexes can be added with a trigger
- Relationships can be added to createdBy, updatedBy, deletedBy fields (https://github.com/celalo/mongolayer#relationships)
- wss://a2dtdqpel0va4i-ats.iot.us-east-1.amazonaws.com/mqtt?... endpointi cloudfront CNAME altına alınabilir (aws does not support this right now)
- https://docs.aws.amazon.com/vpc/latest/userguide/vpc-recommended-nacl-rules.html
- https://lodash.com/custom-builds
- add copyright notice to API docs
- https://www.npmjs.com/package/@codegenie/serverless-express
- https://github.com/naugtur/blocked-at
- batarya seviyesi kontrolü için bir JWT tabanlı cihaz spesifik bir URL oluşturulabilir.
